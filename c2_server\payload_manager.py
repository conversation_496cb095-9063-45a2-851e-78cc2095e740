#!/usr/bin/env python3
"""
Enhanced Payload Manager for C2 Server
Manages payloads from the payloads folder with full CRUD operations
"""

import os
import json
import hashlib
import time
from datetime import datetime
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class PayloadManager:
    """Enhanced payload management with file-based storage"""
    
    def __init__(self, payloads_dir: str = "payloads"):
        self.payloads_dir = payloads_dir
        self.metadata_file = os.path.join(payloads_dir, ".metadata.json")
        self.ensure_payloads_dir()
        self.metadata = self.load_metadata()
    
    def ensure_payloads_dir(self):
        """Ensure payloads directory exists"""
        if not os.path.exists(self.payloads_dir):
            os.makedirs(self.payloads_dir)
            logger.info(f"Created payloads directory: {self.payloads_dir}")
    
    def load_metadata(self) -> Dict:
        """Load payload metadata from file"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading metadata: {e}")
        return {}
    
    def save_metadata(self):
        """Save payload metadata to file"""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving metadata: {e}")
    
    def calculate_file_hash(self, filepath: str) -> str:
        """Calculate SHA256 hash of file"""
        try:
            with open(filepath, 'rb') as f:
                return hashlib.sha256(f.read()).hexdigest()[:16]
        except Exception:
            return "unknown"
    
    def get_file_size(self, filepath: str) -> int:
        """Get file size in bytes"""
        try:
            return os.path.getsize(filepath)
        except Exception:
            return 0
    
    def scan_payloads_folder(self) -> List[Dict]:
        """Scan payloads folder and return payload information"""
        payloads = []
        
        if not os.path.exists(self.payloads_dir):
            return payloads
        
        for filename in os.listdir(self.payloads_dir):
            if filename.startswith('.'):  # Skip hidden files
                continue
                
            filepath = os.path.join(self.payloads_dir, filename)
            if os.path.isfile(filepath):
                # Get file info
                file_size = self.get_file_size(filepath)
                file_hash = self.calculate_file_hash(filepath)
                modified_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                
                # Get metadata if exists
                payload_id = filename
                metadata = self.metadata.get(payload_id, {})
                
                # Determine payload type from extension or content
                payload_type = self.detect_payload_type(filename, filepath)
                
                payload_info = {
                    'id': payload_id,
                    'name': metadata.get('name', filename),
                    'filename': filename,
                    'type': payload_type,
                    'size': file_size,
                    'size_formatted': self.format_file_size(file_size),
                    'hash': file_hash,
                    'created_at': metadata.get('created_at', modified_time.isoformat()),
                    'updated_at': modified_time.isoformat(),
                    'description': metadata.get('description', ''),
                    'platform': metadata.get('platform', 'Windows x64'),
                    'active': metadata.get('active', True),
                    'download_count': metadata.get('download_count', 0),
                    'last_used': metadata.get('last_used', None)
                }
                
                payloads.append(payload_info)
        
        return sorted(payloads, key=lambda x: x['updated_at'], reverse=True)
    
    def detect_payload_type(self, filename: str, filepath: str) -> str:
        """Detect payload type from filename and content"""
        filename_lower = filename.lower()
        
        if 'calc' in filename_lower:
            return 'Calculator'
        elif 'shell' in filename_lower or 'reverse' in filename_lower:
            return 'Reverse Shell'
        elif 'keylog' in filename_lower:
            return 'Keylogger'
        elif 'persist' in filename_lower:
            return 'Persistence'
        elif 'meterpreter' in filename_lower:
            return 'Meterpreter'
        elif filename_lower.endswith('.bin'):
            return 'Binary Shellcode'
        elif filename_lower.endswith('.ps1'):
            return 'PowerShell'
        elif filename_lower.endswith('.exe'):
            return 'Executable'
        else:
            return 'Unknown'
    
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    
    def get_payload_content(self, payload_id: str) -> Optional[bytes]:
        """Get payload content by ID"""
        filepath = os.path.join(self.payloads_dir, payload_id)
        if os.path.exists(filepath):
            try:
                with open(filepath, 'rb') as f:
                    return f.read()
            except Exception as e:
                logger.error(f"Error reading payload {payload_id}: {e}")
        return None
    
    def save_payload(self, payload_id: str, content: bytes, metadata: Dict = None) -> bool:
        """Save payload to file with metadata"""
        try:
            filepath = os.path.join(self.payloads_dir, payload_id)
            
            # Save payload content
            with open(filepath, 'wb') as f:
                f.write(content)
            
            # Update metadata
            if metadata:
                self.metadata[payload_id] = {
                    **metadata,
                    'updated_at': datetime.now().isoformat()
                }
                self.save_metadata()
            
            logger.info(f"Payload saved: {payload_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving payload {payload_id}: {e}")
            return False
    
    def delete_payload(self, payload_id: str) -> bool:
        """Delete payload file and metadata"""
        try:
            filepath = os.path.join(self.payloads_dir, payload_id)
            
            # Delete file
            if os.path.exists(filepath):
                os.remove(filepath)
            
            # Remove metadata
            if payload_id in self.metadata:
                del self.metadata[payload_id]
                self.save_metadata()
            
            logger.info(f"Payload deleted: {payload_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting payload {payload_id}: {e}")
            return False
    
    def update_payload_metadata(self, payload_id: str, metadata: Dict) -> bool:
        """Update payload metadata"""
        try:
            if payload_id not in self.metadata:
                self.metadata[payload_id] = {}
            
            self.metadata[payload_id].update(metadata)
            self.metadata[payload_id]['updated_at'] = datetime.now().isoformat()
            self.save_metadata()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating metadata for {payload_id}: {e}")
            return False
    
    def increment_download_count(self, payload_id: str):
        """Increment download count for payload"""
        if payload_id not in self.metadata:
            self.metadata[payload_id] = {}
        
        self.metadata[payload_id]['download_count'] = self.metadata[payload_id].get('download_count', 0) + 1
        self.metadata[payload_id]['last_used'] = datetime.now().isoformat()
        self.save_metadata()
