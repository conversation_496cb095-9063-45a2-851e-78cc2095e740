#!/usr/bin/env python3
"""
Launch Script - Quick start for C2 Framework
Provides easy access to all components
"""

import os
import sys
import subprocess
import time
import platform

def print_banner():
    """Print C2 framework banner"""
    print("""
🕵️  ═══════════════════════════════════════════════════════════════
    1000% UNTRACEABLE PYTHON C2 FRAMEWORK
    Advanced Stealth • HWID Whitelisting • P2P Network
═══════════════════════════════════════════════════════════════
""")

def launch_c2_server():
    """Launch C2 server"""
    print("🚀 Launching Stealth C2 Server...")
    try:
        os.chdir('c2_server')
        subprocess.run([sys.executable, 'server.py'])
    except KeyboardInterrupt:
        print("\n[!] C2 Server stopped by user")
    except Exception as e:
        print(f"[-] Error launching C2 server: {e}")

def launch_loader():
    """Launch loader"""
    print("🚀 Launching Stealth Loader...")
    try:
        os.chdir('loader')
        subprocess.run([sys.executable, 'loader.py'])
    except KeyboardInterrupt:
        print("\n[!] Loader stopped by user")
    except Exception as e:
        print(f"[-] Error launching loader: {e}")

def launch_trusted_client():
    """Launch trusted client"""
    print("🚀 Launching Trusted Client...")
    try:
        subprocess.run([sys.executable, 'utils/trusted_client.py'])
    except Exception as e:
        print(f"[-] Error launching trusted client: {e}")

def setup_hwid():
    """Setup HWID whitelisting"""
    print("🚀 Setting up HWID whitelisting...")
    try:
        subprocess.run([sys.executable, 'utils/hwid_manager.py'])
    except Exception as e:
        print(f"[-] Error setting up HWID: {e}")

def show_menu():
    """Show main menu"""
    print_banner()
    print("Select an option:")
    print()
    print("1. 🖥️  Start C2 Server")
    print("2. 📱 Deploy Loader")
    print("3. 🔐 Trusted Client Access")
    print("4. 🔒 Setup HWID Whitelisting")
    print("5. ⚙️  Run Full Setup")
    print("6. 📊 Open Dashboard (Browser)")
    print("7. ❌ Exit")
    print()

def open_dashboard():
    """Open dashboard in browser"""
    import webbrowser
    print("🌐 Opening C2 Dashboard...")
    webbrowser.open('http://localhost:8080')

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("[-] Python 3.7 or higher is required")
        return False

    print(f"[+] Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def install_dependencies():
    """Install dependencies for both C2 server and loader"""
    print("[+] Installing C2 Server dependencies...")
    success, output = run_command("pip install -r requirements.txt", cwd="c2_server")
    if not success:
        print(f"[-] Failed to install C2 server dependencies: {output}")
        return False

    print("[+] Installing Loader dependencies...")
    success, output = run_command("pip install -r requirements.txt", cwd="loader")
    if not success:
        print(f"[-] Failed to install loader dependencies: {output}")
        return False

    print("[+] All dependencies installed successfully!")
    return True

def run_command(command, cwd=None):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def create_directories():
    """Create necessary directories"""
    directories = [
        "c2_server/logs",
        "c2_server/payloads",
        "loader/logs"
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"[+] Created directory: {directory}")

def setup_permissions():
    """Set up file permissions (Unix-like systems)"""
    if platform.system() != "Windows":
        # Make scripts executable
        scripts = [
            "c2_server/server.py",
            "loader/loader.py"
        ]

        for script in scripts:
            os.chmod(script, 0o755)
            print(f"[+] Made executable: {script}")

def run_full_setup():
    """Run complete setup process"""
    print("🚀 Running Full C2 Framework Setup...")
    print("="*60)

    # Check Python version
    if not check_python_version():
        return False

    # Create directories
    create_directories()

    # Install dependencies
    if not install_dependencies():
        return False

    # Set up permissions
    setup_permissions()

    # Setup HWID whitelisting
    setup_hwid()

    print("\n🎉 Setup Complete!")
    print("🛡️  Your C2 framework is ready with maximum stealth!")
    return True

def main():
    """Main launcher function"""
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-7): ").strip()
            
            if choice == '1':
                launch_c2_server()
            elif choice == '2':
                launch_loader()
            elif choice == '3':
                launch_trusted_client()
            elif choice == '4':
                setup_hwid()
            elif choice == '5':
                run_full_setup()
            elif choice == '6':
                open_dashboard()
            elif choice == '7':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
            
            if choice != '7':
                input("\nPress Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
