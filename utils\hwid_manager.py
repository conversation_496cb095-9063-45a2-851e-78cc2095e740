#!/usr/bin/env python3
"""
HWID Manager - Hardware ID based whitelisting for C2 access
Allows trusted operators to bypass all stealth protections
"""

import hashlib
import platform
import uuid
import subprocess
import os
import json
from datetime import datetime

class HWIDManager:
    def __init__(self):
        self.whitelist_file = "hwid_whitelist.json"
        self.trusted_hwids = self.load_whitelist()
        
    def get_system_hwid(self):
        """Generate unique hardware ID for current system"""
        try:
            # Get multiple hardware identifiers
            identifiers = []
            
            # MAC Address
            mac = hex(uuid.getnode())[2:]
            identifiers.append(f"mac:{mac}")
            
            # CPU Info
            try:
                if platform.system() == "Windows":
                    cpu_info = subprocess.check_output("wmic cpu get ProcessorId", shell=True).decode().strip()
                    cpu_id = cpu_info.split('\n')[1].strip() if len(cpu_info.split('\n')) > 1 else "unknown"
                elif platform.system() == "Linux":
                    cpu_info = subprocess.check_output("cat /proc/cpuinfo | grep 'processor' | wc -l", shell=True).decode().strip()
                    cpu_id = cpu_info
                else:
                    cpu_id = platform.processor()
                identifiers.append(f"cpu:{cpu_id}")
            except:
                identifiers.append(f"cpu:unknown")
            
            # Motherboard Serial (Windows)
            try:
                if platform.system() == "Windows":
                    mb_serial = subprocess.check_output("wmic baseboard get serialnumber", shell=True).decode().strip()
                    mb_id = mb_serial.split('\n')[1].strip() if len(mb_serial.split('\n')) > 1 else "unknown"
                    identifiers.append(f"mb:{mb_id}")
            except:
                pass
            
            # System UUID
            try:
                if platform.system() == "Windows":
                    sys_uuid = subprocess.check_output("wmic csproduct get uuid", shell=True).decode().strip()
                    uuid_id = sys_uuid.split('\n')[1].strip() if len(sys_uuid.split('\n')) > 1 else "unknown"
                elif platform.system() == "Linux":
                    try:
                        with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                            uuid_id = f.read().strip()
                    except:
                        uuid_id = "unknown"
                else:
                    uuid_id = "unknown"
                identifiers.append(f"uuid:{uuid_id}")
            except:
                pass
            
            # Hostname
            identifiers.append(f"host:{platform.node()}")
            
            # Username
            identifiers.append(f"user:{os.getenv('USERNAME', os.getenv('USER', 'unknown'))}")
            
            # Create composite HWID
            hwid_string = "|".join(identifiers)
            hwid_hash = hashlib.sha256(hwid_string.encode()).hexdigest()
            
            return hwid_hash
            
        except Exception as e:
            # Fallback HWID
            fallback = f"{platform.node()}_{uuid.getnode()}_{platform.system()}"
            return hashlib.sha256(fallback.encode()).hexdigest()
    
    def get_detailed_system_info(self):
        """Get detailed system information for HWID registration"""
        info = {
            'hwid': self.get_system_hwid(),
            'hostname': platform.node(),
            'username': os.getenv('USERNAME', os.getenv('USER', 'unknown')),
            'os': f"{platform.system()} {platform.release()}",
            'architecture': platform.architecture()[0],
            'processor': platform.processor(),
            'mac_address': hex(uuid.getnode())[2:],
            'timestamp': datetime.now().isoformat()
        }
        
        # Add Windows-specific info
        if platform.system() == "Windows":
            try:
                cpu_info = subprocess.check_output("wmic cpu get ProcessorId", shell=True).decode().strip()
                info['cpu_id'] = cpu_info.split('\n')[1].strip() if len(cpu_info.split('\n')) > 1 else "unknown"
                
                mb_serial = subprocess.check_output("wmic baseboard get serialnumber", shell=True).decode().strip()
                info['motherboard'] = mb_serial.split('\n')[1].strip() if len(mb_serial.split('\n')) > 1 else "unknown"
                
                sys_uuid = subprocess.check_output("wmic csproduct get uuid", shell=True).decode().strip()
                info['system_uuid'] = sys_uuid.split('\n')[1].strip() if len(sys_uuid.split('\n')) > 1 else "unknown"
            except:
                pass
        
        return info
    
    def load_whitelist(self):
        """Load HWID whitelist from file"""
        try:
            if os.path.exists(self.whitelist_file):
                with open(self.whitelist_file, 'r') as f:
                    data = json.load(f)
                    return data.get('trusted_hwids', [])
        except:
            pass
        return []
    
    def save_whitelist(self):
        """Save HWID whitelist to file"""
        try:
            data = {
                'trusted_hwids': self.trusted_hwids,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.whitelist_file, 'w') as f:
                json.dump(data, f, indent=2)
            return True
        except:
            return False
    
    def add_current_system(self, alias=None):
        """Add current system to whitelist"""
        hwid = self.get_system_hwid()
        system_info = self.get_detailed_system_info()
        
        # Check if already exists
        for entry in self.trusted_hwids:
            if entry['hwid'] == hwid:
                print(f"[+] System already whitelisted: {entry['alias']}")
                return hwid
        
        # Add new entry
        entry = {
            'hwid': hwid,
            'alias': alias or f"{system_info['username']}@{system_info['hostname']}",
            'system_info': system_info,
            'added_date': datetime.now().isoformat()
        }
        
        self.trusted_hwids.append(entry)
        self.save_whitelist()
        
        print(f"[+] Added system to whitelist: {entry['alias']}")
        print(f"[+] HWID: {hwid}")
        
        return hwid
    
    def is_trusted_system(self, hwid=None):
        """Check if system is trusted"""
        if hwid is None:
            hwid = self.get_system_hwid()
        
        for entry in self.trusted_hwids:
            if entry['hwid'] == hwid:
                return True, entry['alias']
        
        return False, None
    
    def remove_system(self, hwid):
        """Remove system from whitelist"""
        self.trusted_hwids = [entry for entry in self.trusted_hwids if entry['hwid'] != hwid]
        self.save_whitelist()
    
    def list_trusted_systems(self):
        """List all trusted systems"""
        print("\n🔒 Trusted Systems (HWID Whitelist):")
        print("="*60)
        
        if not self.trusted_hwids:
            print("No trusted systems found.")
            return
        
        for i, entry in enumerate(self.trusted_hwids, 1):
            print(f"{i}. {entry['alias']}")
            print(f"   HWID: {entry['hwid']}")
            print(f"   OS: {entry['system_info']['os']}")
            print(f"   Added: {entry['added_date']}")
            print()
    
    def generate_hwid_header(self, hwid=None):
        """Generate HWID header for HTTP requests"""
        if hwid is None:
            hwid = self.get_system_hwid()
        
        # Create signed HWID header
        timestamp = str(int(datetime.now().timestamp()))
        signature = hashlib.sha256(f"{hwid}:{timestamp}".encode()).hexdigest()[:16]
        
        return f"HWID-{hwid[:16]}-{timestamp}-{signature}"
    
    def validate_hwid_header(self, header_value):
        """Validate HWID header from HTTP request"""
        try:
            if not header_value.startswith("HWID-"):
                return False, None
            
            parts = header_value[5:].split('-')
            if len(parts) != 3:
                return False, None
            
            hwid_prefix, timestamp, signature = parts
            
            # Find matching HWID
            for entry in self.trusted_hwids:
                if entry['hwid'].startswith(hwid_prefix):
                    # Validate signature
                    expected_sig = hashlib.sha256(f"{entry['hwid']}:{timestamp}".encode()).hexdigest()[:16]
                    if signature == expected_sig:
                        # Check timestamp (allow 1 hour window)
                        current_time = int(datetime.now().timestamp())
                        request_time = int(timestamp)
                        if abs(current_time - request_time) < 3600:  # 1 hour
                            return True, entry['alias']
            
            return False, None
            
        except:
            return False, None

def setup_hwid_whitelist():
    """Interactive setup for HWID whitelisting"""
    print("🔒 HWID Whitelist Setup - C2 Operator Authentication")
    print("="*60)
    
    hwid_manager = HWIDManager()
    
    # Show current system info
    system_info = hwid_manager.get_detailed_system_info()
    print(f"\n📋 Current System Information:")
    print(f"   Hostname: {system_info['hostname']}")
    print(f"   Username: {system_info['username']}")
    print(f"   OS: {system_info['os']}")
    print(f"   HWID: {system_info['hwid']}")
    
    # Check if already trusted
    is_trusted, alias = hwid_manager.is_trusted_system()
    if is_trusted:
        print(f"\n✅ This system is already trusted as: {alias}")
    else:
        print(f"\n⚠️  This system is NOT in the whitelist")
        
        # Add current system
        alias = input("\nEnter alias for this system (or press Enter for default): ").strip()
        if not alias:
            alias = f"{system_info['username']}@{system_info['hostname']}"
        
        hwid_manager.add_current_system(alias)
        print(f"\n✅ System added to whitelist successfully!")
    
    # Show all trusted systems
    hwid_manager.list_trusted_systems()
    
    print(f"\n🎯 HWID whitelist saved to: {hwid_manager.whitelist_file}")
    print("🛡️  Your system will now bypass ALL stealth protections!")
    
    return hwid_manager

if __name__ == "__main__":
    setup_hwid_whitelist()
