#!/usr/bin/env python3
"""
P2P Node for C2 Server
Implements peer-to-peer networking for untraceable communication
"""

import socket
import threading
import json
import time
import hashlib
import random
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class P2PNode:
    def __init__(self, port=9999, max_peers=10):
        self.port = port
        self.max_peers = max_peers
        self.peers = {}  # {peer_id: {'socket': socket, 'address': (ip, port), 'last_seen': datetime}}
        self.node_id = self.generate_node_id()
        self.running = False
        self.server_socket = None
        self.bootstrap_nodes = [
            # Add bootstrap nodes here for initial connection
            # ('127.0.0.1', 9999),
        ]
        self.message_handlers = {
            'ping': self.handle_ping,
            'pong': self.handle_pong,
            'peer_discovery': self.handle_peer_discovery,
            'data_relay': self.handle_data_relay,
            'node_announce': self.handle_node_announce
        }
        
    def generate_node_id(self):
        """Generate unique node ID"""
        return hashlib.sha256(f"{time.time()}{random.random()}".encode()).hexdigest()[:16]
    
    def start(self):
        """Start P2P node"""
        self.running = True
        logger.info(f"Starting P2P node {self.node_id} on port {self.port}")
        
        # Start server thread
        server_thread = threading.Thread(target=self.start_server)
        server_thread.daemon = True
        server_thread.start()
        
        # Start peer maintenance thread
        maintenance_thread = threading.Thread(target=self.peer_maintenance)
        maintenance_thread.daemon = True
        maintenance_thread.start()
        
        # Connect to bootstrap nodes
        self.connect_to_bootstrap_nodes()
        
        # Start peer discovery
        discovery_thread = threading.Thread(target=self.peer_discovery_loop)
        discovery_thread.daemon = True
        discovery_thread.start()
    
    def start_server(self):
        """Start listening for incoming connections"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('0.0.0.0', self.port))
            self.server_socket.listen(5)
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    client_thread = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                except Exception as e:
                    if self.running:
                        logger.error(f"Error accepting connection: {e}")
                        
        except Exception as e:
            logger.error(f"Error starting P2P server: {e}")
    
    def handle_client(self, client_socket, address):
        """Handle incoming client connection"""
        try:
            while self.running:
                data = client_socket.recv(4096)
                if not data:
                    break
                
                try:
                    message = json.loads(data.decode())
                    self.process_message(message, client_socket, address)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON from {address}")
                    
        except Exception as e:
            logger.error(f"Error handling client {address}: {e}")
        finally:
            client_socket.close()
            self.remove_peer_by_address(address)
    
    def process_message(self, message, socket_obj, address):
        """Process incoming message"""
        msg_type = message.get('type')
        if msg_type in self.message_handlers:
            self.message_handlers[msg_type](message, socket_obj, address)
        else:
            logger.warning(f"Unknown message type: {msg_type}")
    
    def handle_ping(self, message, socket_obj, address):
        """Handle ping message"""
        peer_id = message.get('node_id')
        if peer_id and peer_id != self.node_id:
            self.add_peer(peer_id, socket_obj, address)
            self.send_message(socket_obj, {
                'type': 'pong',
                'node_id': self.node_id,
                'timestamp': time.time()
            })
    
    def handle_pong(self, message, socket_obj, address):
        """Handle pong message"""
        peer_id = message.get('node_id')
        if peer_id and peer_id != self.node_id:
            self.add_peer(peer_id, socket_obj, address)
    
    def handle_peer_discovery(self, message, socket_obj, address):
        """Handle peer discovery request"""
        peer_list = []
        for peer_id, peer_info in list(self.peers.items())[:5]:  # Send max 5 peers
            peer_list.append({
                'node_id': peer_id,
                'address': peer_info['address']
            })
        
        self.send_message(socket_obj, {
            'type': 'peer_list',
            'peers': peer_list,
            'node_id': self.node_id
        })
    
    def handle_data_relay(self, message, socket_obj, address):
        """Handle data relay message"""
        target_id = message.get('target_id')
        data = message.get('data')
        
        if target_id == self.node_id:
            # Message is for us
            logger.info(f"Received relayed data: {data}")
        else:
            # Relay to other peers
            self.relay_message(message, exclude_address=address)
    
    def handle_node_announce(self, message, socket_obj, address):
        """Handle node announcement"""
        peer_id = message.get('node_id')
        if peer_id and peer_id != self.node_id:
            self.add_peer(peer_id, socket_obj, address)
            logger.info(f"New node announced: {peer_id}")
    
    def add_peer(self, peer_id, socket_obj, address):
        """Add peer to peer list"""
        if len(self.peers) < self.max_peers and peer_id not in self.peers:
            self.peers[peer_id] = {
                'socket': socket_obj,
                'address': address,
                'last_seen': datetime.now()
            }
            logger.info(f"Added peer: {peer_id} from {address}")
    
    def remove_peer(self, peer_id):
        """Remove peer from peer list"""
        if peer_id in self.peers:
            try:
                self.peers[peer_id]['socket'].close()
            except:
                pass
            del self.peers[peer_id]
            logger.info(f"Removed peer: {peer_id}")
    
    def remove_peer_by_address(self, address):
        """Remove peer by address"""
        for peer_id, peer_info in list(self.peers.items()):
            if peer_info['address'] == address:
                self.remove_peer(peer_id)
                break
    
    def send_message(self, socket_obj, message):
        """Send message to peer"""
        try:
            data = json.dumps(message).encode()
            socket_obj.send(data)
        except Exception as e:
            logger.error(f"Error sending message: {e}")
    
    def broadcast_message(self, message):
        """Broadcast message to all peers"""
        for peer_id, peer_info in list(self.peers.items()):
            try:
                self.send_message(peer_info['socket'], message)
            except Exception as e:
                logger.error(f"Error broadcasting to {peer_id}: {e}")
                self.remove_peer(peer_id)
    
    def relay_message(self, message, exclude_address=None):
        """Relay message to random peers"""
        available_peers = [
            (peer_id, peer_info) for peer_id, peer_info in self.peers.items()
            if peer_info['address'] != exclude_address
        ]
        
        # Relay to random subset of peers
        relay_count = min(3, len(available_peers))
        relay_peers = random.sample(available_peers, relay_count)
        
        for peer_id, peer_info in relay_peers:
            try:
                self.send_message(peer_info['socket'], message)
            except Exception as e:
                logger.error(f"Error relaying to {peer_id}: {e}")
                self.remove_peer(peer_id)
    
    def connect_to_bootstrap_nodes(self):
        """Connect to bootstrap nodes"""
        for address in self.bootstrap_nodes:
            try:
                self.connect_to_peer(address)
            except Exception as e:
                logger.warning(f"Failed to connect to bootstrap node {address}: {e}")
    
    def connect_to_peer(self, address):
        """Connect to a specific peer"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(address)
            
            # Send ping
            self.send_message(sock, {
                'type': 'ping',
                'node_id': self.node_id,
                'timestamp': time.time()
            })
            
            # Handle connection in separate thread
            client_thread = threading.Thread(
                target=self.handle_client,
                args=(sock, address)
            )
            client_thread.daemon = True
            client_thread.start()
            
        except Exception as e:
            logger.error(f"Error connecting to peer {address}: {e}")
    
    def peer_discovery_loop(self):
        """Continuously discover new peers"""
        while self.running:
            if len(self.peers) < self.max_peers:
                # Request peer discovery from existing peers
                discovery_message = {
                    'type': 'peer_discovery',
                    'node_id': self.node_id,
                    'timestamp': time.time()
                }
                self.broadcast_message(discovery_message)
            
            time.sleep(30)  # Discovery every 30 seconds
    
    def peer_maintenance(self):
        """Maintain peer connections"""
        while self.running:
            current_time = datetime.now()
            
            # Remove stale peers
            stale_peers = []
            for peer_id, peer_info in self.peers.items():
                if (current_time - peer_info['last_seen']).seconds > 300:  # 5 minutes
                    stale_peers.append(peer_id)
            
            for peer_id in stale_peers:
                self.remove_peer(peer_id)
            
            # Send periodic pings
            ping_message = {
                'type': 'ping',
                'node_id': self.node_id,
                'timestamp': time.time()
            }
            self.broadcast_message(ping_message)
            
            time.sleep(60)  # Maintenance every minute
    
    def stop(self):
        """Stop P2P node"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        
        # Close all peer connections
        for peer_id in list(self.peers.keys()):
            self.remove_peer(peer_id)
        
        logger.info("P2P node stopped")
