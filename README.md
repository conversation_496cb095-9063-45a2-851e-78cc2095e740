# Python C2 Framework

A completely Python-based Command and Control (C2) framework with peer-to-peer (P2P) networking capabilities for educational and research purposes.

## ⚠️ DISCLAIMER

This software is provided for educational and research purposes only. The authors are not responsible for any misuse or damage caused by this software. Use at your own risk and ensure compliance with all applicable laws and regulations.

## Features

### C2 Server
- **Web Dashboard**: Real-time monitoring of infected hosts and statistics
- **P2P Networking**: Decentralized communication for enhanced stealth
- **SQLite Database**: Persistent storage of host information and commands
- **RESTful API**: Clean API for loader communication
- **Modular Design**: Easy to extend and customize

### Loader
- **Cross-Platform**: Works on Windows, Linux, and macOS
- **Persistence**: Automatic persistence mechanisms for each OS
- **P2P Fallback**: Uses P2P network when C2 server is unavailable
- **Encrypted Communication**: All communications are encrypted
- **Stealth Features**: Multiple evasion techniques

### P2P Network
- **Distributed Hash Table**: For node discovery and routing
- **Encrypted Messaging**: All P2P communications are encrypted
- **Redundant Routing**: Multiple paths for message delivery
- **Bootstrap Nodes**: Initial connection points for the network

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   C2 Server     │    │   P2P Network   │    │     Loader      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Web Dashboard │ │    │ Node Discovery│ │    │ Payload Exec│ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │  Database   │ │◄──►│ │ Encrypted   │ │◄──►│ │ Persistence │ │
│ └─────────────┘ │    │ │ Messaging   │ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ └─────────────┘ │    │ ┌─────────────┐ │
│ │ REST API    │ │    │ ┌─────────────┐ │    │ │ Crypto      │ │
│ └─────────────┘ │    │ │ Redundant   │ │    │ └─────────────┘ │
└─────────────────┘    │ │ Routing     │ │    └─────────────────┘
                       │ └─────────────┘ │
                       └─────────────────┘
```

## Installation

### C2 Server Setup

1. Navigate to the c2_server directory:
```bash
cd c2_server
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Start the C2 server:
```bash
python server.py
```

The web dashboard will be available at `http://localhost:8080`

### Loader Setup

1. Navigate to the loader directory:
```bash
cd loader
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure the loader by editing the C2 server addresses in `loader.py`

4. Run the loader:
```bash
python loader.py
```

## Configuration

### C2 Server Configuration

Edit `c2_server/server.py` to modify:
- Server host and port
- P2P port
- Database settings
- Payload storage

### Loader Configuration

Edit `loader/loader.py` to modify:
- C2 server addresses
- P2P bootstrap nodes
- Heartbeat intervals
- Persistence settings

## Usage

### Starting the C2 Server

```bash
cd c2_server
python server.py
```

### Deploying the Loader

```bash
cd loader
python loader.py
```

### Accessing the Dashboard

Open your web browser and navigate to `http://localhost:8080` to access the C2 dashboard.

### Loading Payloads

Payloads can be loaded through the C2 server API:

```python
import requests

payload_data = "\\x90\\x90\\x90\\x90"  # Your shellcode here
response = requests.post('http://localhost:8080/api/load_payload', json={
    'payload_id': 'custom_payload',
    'payload': payload_data
})
```

## Security Features

### Encryption
- **Symmetric Encryption**: AES-256 via Fernet
- **Asymmetric Encryption**: RSA-2048 for key exchange
- **HMAC**: Message authentication codes for integrity

### Stealth Techniques
- **Domain Fronting**: Hide real C2 destination
- **Tor Support**: Route traffic through Tor network
- **DNS over HTTPS**: Encrypted DNS resolution
- **Random User Agents**: Mimic legitimate browser traffic
- **Jitter**: Random delays to avoid pattern detection

### P2P Features
- **Distributed Architecture**: No single point of failure
- **Encrypted Messaging**: All P2P communications encrypted
- **Node Discovery**: Automatic peer discovery and connection
- **Redundant Routing**: Multiple paths for message delivery

## File Structure

```
├── c2_server/
│   ├── server.py          # Main C2 server
│   ├── database.py        # Database operations
│   ├── p2p_node.py        # P2P networking
│   └── requirements.txt   # Dependencies
├── loader/
│   ├── loader.py          # Main loader
│   ├── network.py         # Network communication
│   ├── crypto.py          # Encryption utilities
│   └── requirements.txt   # Dependencies
└── README.md              # This file
```

## API Endpoints

### C2 Server API

- `GET /` - Web dashboard
- `POST /api/register` - Register new infected host
- `POST /api/heartbeat` - Host heartbeat
- `GET /api/payload/<id>` - Download payload
- `GET /api/stats` - Get system statistics

## Development

### Adding New Features

1. **New Commands**: Add command handlers in `c2_server/server.py`
2. **New Payloads**: Extend payload management in the database module
3. **New Persistence**: Add OS-specific persistence in `loader/loader.py`
4. **New Crypto**: Extend encryption methods in `loader/crypto.py`

### Testing

Test the framework in isolated environments:

1. Start the C2 server
2. Run the loader
3. Monitor connections in the web dashboard
4. Test P2P fallback by stopping the C2 server

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check firewall settings and port availability
2. **Import Errors**: Ensure all dependencies are installed
3. **Database Errors**: Check file permissions for SQLite database
4. **P2P Issues**: Verify network connectivity between nodes

### Logs

Check console output for detailed error messages and debugging information.

## Legal Notice

This software is intended for educational and authorized testing purposes only. Users are responsible for ensuring compliance with all applicable laws and regulations. The authors disclaim any responsibility for misuse of this software.

## Contributing

Contributions are welcome! Please ensure all contributions are for legitimate educational or research purposes.

## License

This project is provided as-is for educational purposes. Use responsibly and in accordance with applicable laws.
