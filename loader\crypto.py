#!/usr/bin/env python3
"""
Crypto module for Loader
Handles encryption, decryption, and obfuscation for secure communication
"""

import os
import base64
import hashlib
import hmac
import random
import string
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class CryptoManager:
    def __init__(self):
        self.symmetric_key = None
        self.private_key = None
        self.public_key = None
        self.generate_keys()
        
    def generate_keys(self):
        """Generate RSA key pair for asymmetric encryption"""
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        self.public_key = self.private_key.public_key()
        
    def generate_symmetric_key(self, password=None):
        """Generate symmetric encryption key"""
        if password:
            # Derive key from password
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        else:
            # Generate random key
            key = Fernet.generate_key()
        
        self.symmetric_key = key
        return key
    
    def encrypt_symmetric(self, data, key=None):
        """Encrypt data using symmetric encryption"""
        if key is None:
            if self.symmetric_key is None:
                self.generate_symmetric_key()
            key = self.symmetric_key
        
        fernet = Fernet(key)
        
        if isinstance(data, str):
            data = data.encode()
        
        encrypted = fernet.encrypt(data)
        return base64.b64encode(encrypted).decode()
    
    def decrypt_symmetric(self, encrypted_data, key=None):
        """Decrypt data using symmetric encryption"""
        if key is None:
            key = self.symmetric_key
        
        if key is None:
            raise ValueError("No symmetric key available")
        
        fernet = Fernet(key)
        
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted = fernet.decrypt(encrypted_bytes)
        
        return decrypted.decode()
    
    def encrypt_asymmetric(self, data, public_key=None):
        """Encrypt data using RSA public key"""
        if public_key is None:
            public_key = self.public_key
        
        if isinstance(data, str):
            data = data.encode()
        
        encrypted = public_key.encrypt(
            data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        return base64.b64encode(encrypted).decode()
    
    def decrypt_asymmetric(self, encrypted_data):
        """Decrypt data using RSA private key"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        
        decrypted = self.private_key.decrypt(
            encrypted_bytes,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        return decrypted.decode()
    
    def get_public_key_pem(self):
        """Get public key in PEM format"""
        pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        return pem.decode()
    
    def load_public_key_pem(self, pem_data):
        """Load public key from PEM format"""
        return serialization.load_pem_public_key(pem_data.encode())
    
    def generate_hmac(self, data, key=None):
        """Generate HMAC for data integrity"""
        if key is None:
            key = self.symmetric_key or os.urandom(32)
        
        if isinstance(data, str):
            data = data.encode()
        
        if isinstance(key, str):
            key = key.encode()
        
        signature = hmac.new(key, data, hashlib.sha256).hexdigest()
        return signature
    
    def verify_hmac(self, data, signature, key=None):
        """Verify HMAC signature"""
        if key is None:
            key = self.symmetric_key
        
        expected_signature = self.generate_hmac(data, key)
        return hmac.compare_digest(signature, expected_signature)
    
    def hash_data(self, data, algorithm='sha256'):
        """Hash data using specified algorithm"""
        if isinstance(data, str):
            data = data.encode()
        
        if algorithm == 'sha256':
            return hashlib.sha256(data).hexdigest()
        elif algorithm == 'sha512':
            return hashlib.sha512(data).hexdigest()
        elif algorithm == 'md5':
            return hashlib.md5(data).hexdigest()
        else:
            raise ValueError(f"Unsupported hash algorithm: {algorithm}")

class Obfuscator:
    """Code and data obfuscation utilities"""
    
    @staticmethod
    def xor_encrypt(data, key):
        """Simple XOR encryption"""
        if isinstance(data, str):
            data = data.encode()
        
        if isinstance(key, str):
            key = key.encode()
        
        # Repeat key to match data length
        key = (key * (len(data) // len(key) + 1))[:len(data)]
        
        result = bytes(a ^ b for a, b in zip(data, key))
        return base64.b64encode(result).decode()
    
    @staticmethod
    def xor_decrypt(encrypted_data, key):
        """Simple XOR decryption"""
        data = base64.b64decode(encrypted_data.encode())
        
        if isinstance(key, str):
            key = key.encode()
        
        # Repeat key to match data length
        key = (key * (len(data) // len(key) + 1))[:len(data)]
        
        result = bytes(a ^ b for a, b in zip(data, key))
        return result.decode()
    
    @staticmethod
    def base64_encode_multiple(data, iterations=3):
        """Multiple rounds of base64 encoding"""
        if isinstance(data, str):
            data = data.encode()
        
        for _ in range(iterations):
            data = base64.b64encode(data)
        
        return data.decode()
    
    @staticmethod
    def base64_decode_multiple(encoded_data, iterations=3):
        """Multiple rounds of base64 decoding"""
        data = encoded_data.encode()
        
        for _ in range(iterations):
            data = base64.b64decode(data)
        
        return data.decode()
    
    @staticmethod
    def string_obfuscate(text):
        """Obfuscate string by character substitution"""
        # Create character mapping
        chars = string.ascii_letters + string.digits
        mapping = {}
        shuffled = list(chars)
        random.shuffle(shuffled)
        
        for i, char in enumerate(chars):
            mapping[char] = shuffled[i]
        
        # Apply mapping
        obfuscated = ''.join(mapping.get(c, c) for c in text)
        
        return obfuscated, mapping
    
    @staticmethod
    def string_deobfuscate(obfuscated_text, mapping):
        """Deobfuscate string using character mapping"""
        reverse_mapping = {v: k for k, v in mapping.items()}
        return ''.join(reverse_mapping.get(c, c) for c in obfuscated_text)
    
    @staticmethod
    def add_junk_code(code):
        """Add junk code to obfuscate real functionality"""
        junk_lines = [
            "import random",
            "import time",
            "x = random.randint(1, 100)",
            "time.sleep(0.001)",
            "dummy_var = 'obfuscation'",
            "if random.random() > 0.5: pass",
            "temp_list = [i for i in range(10)]",
            "temp_dict = {'key': 'value'}",
        ]
        
        lines = code.split('\n')
        result = []
        
        for line in lines:
            result.append(line)
            if random.random() > 0.7:  # 30% chance to add junk
                result.append(random.choice(junk_lines))
        
        return '\n'.join(result)

class StealthCrypto:
    """Advanced cryptographic techniques for stealth"""
    
    @staticmethod
    def steganography_hide(cover_text, secret_text):
        """Hide secret text in cover text using steganography"""
        # Convert secret to binary
        secret_binary = ''.join(format(ord(c), '08b') for c in secret_text)
        
        # Add delimiter
        secret_binary += '1111111111111110'  # Delimiter
        
        result = []
        secret_index = 0
        
        for char in cover_text:
            if secret_index < len(secret_binary):
                # Modify character based on secret bit
                if secret_binary[secret_index] == '1':
                    # Add invisible character or modify spacing
                    result.append(char + '\u200b')  # Zero-width space
                else:
                    result.append(char)
                secret_index += 1
            else:
                result.append(char)
        
        return ''.join(result)
    
    @staticmethod
    def steganography_extract(stego_text):
        """Extract secret text from steganographic text"""
        binary_secret = ''
        
        for char in stego_text:
            if '\u200b' in char:  # Zero-width space found
                binary_secret += '1'
            else:
                binary_secret += '0'
        
        # Find delimiter
        delimiter = '1111111111111110'
        delimiter_pos = binary_secret.find(delimiter)
        
        if delimiter_pos == -1:
            return None
        
        # Extract secret binary
        secret_binary = binary_secret[:delimiter_pos]
        
        # Convert binary to text
        secret_text = ''
        for i in range(0, len(secret_binary), 8):
            byte = secret_binary[i:i+8]
            if len(byte) == 8:
                secret_text += chr(int(byte, 2))
        
        return secret_text
    
    @staticmethod
    def polymorphic_encrypt(data, key):
        """Polymorphic encryption that changes each time"""
        # Generate random transformation
        transform_type = random.choice(['xor', 'caesar', 'reverse'])
        
        if transform_type == 'xor':
            result = Obfuscator.xor_encrypt(data, key)
            method = 'xor'
        elif transform_type == 'caesar':
            shift = random.randint(1, 25)
            result = StealthCrypto.caesar_cipher(data, shift)
            method = f'caesar_{shift}'
        else:  # reverse
            result = data[::-1]
            method = 'reverse'
        
        # Encode method in result
        return f"{method}:{result}"
    
    @staticmethod
    def polymorphic_decrypt(encrypted_data, key):
        """Decrypt polymorphic encryption"""
        method, data = encrypted_data.split(':', 1)
        
        if method == 'xor':
            return Obfuscator.xor_decrypt(data, key)
        elif method.startswith('caesar_'):
            shift = int(method.split('_')[1])
            return StealthCrypto.caesar_cipher(data, -shift)
        elif method == 'reverse':
            return data[::-1]
        else:
            raise ValueError(f"Unknown encryption method: {method}")
    
    @staticmethod
    def caesar_cipher(text, shift):
        """Caesar cipher implementation"""
        result = ""
        for char in text:
            if char.isalpha():
                ascii_offset = 65 if char.isupper() else 97
                result += chr((ord(char) - ascii_offset + shift) % 26 + ascii_offset)
            else:
                result += char
        return result
